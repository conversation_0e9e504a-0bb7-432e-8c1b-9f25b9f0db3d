# OpenHands Cloud

OpenHands Cloud is the hosted cloud version of All Hands AI's OpenHands.

## Accessing OpenHands Cloud

To get started with OpenHands Cloud, visit [app.all-hands.dev](https://app.all-hands.dev).

You'll be prompted to connect with your GitHub or GitLab account:

1. Click `Log in with GitHub` or `Log in with GitLab`.
2. Review the permissions requested by OpenHands and authorize the application.
   - OpenHands will require certain permissions from your account. To read more about these permissions,
     you can click the `Learn more` link on the authorization page.

## Next Steps

Once you've connected your account, you can:

- [Install GitHub Integration](./github-installation.md) to use OpenHands with your GitHub repositories
- [Install GitLab Integration](./gitlab-installation.md) to use OpenHands with your GitLab repositories
- [Access the Cloud UI](./cloud-ui.md) to interact with the web interface
- [Use the Cloud API](./cloud-api.md) to programmatically interact with OpenHands
- [Set up the Cloud Issue Resolver](./cloud-issue-resolver.md) to automate code fixes and provide intelligent assistance
