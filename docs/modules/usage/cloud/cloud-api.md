# Cloud API

OpenHands Cloud provides a REST API that allows you to programmatically interact with the service. This is useful if
you want to kick off your own jobs from your programs in a flexible way.

This guide explains how to obtain an API key and use the API to start conversations.
For more detailed information about the API, refer to the [OpenHands API Reference](https://docs.all-hands.dev/swagger-ui/).

## Obtaining an API Key

To use the OpenHands Cloud API, you'll need to generate an API key:

1. Log in to your [OpenHands Cloud](https://app.all-hands.dev) account.
2. Navigate to the [Settings page](https://app.all-hands.dev/settings).
3. Select the `API Keys` tab.
4. Click `Create API Key`.
5. Give your key a descriptive name (Example: "Development" or "Production") and select `Create`.
6. Copy the generated API key and store it securely. It will only be shown once.

![API Key Generation](/img/docs/api-key-generation.png)

## API Usage

### Starting a New Conversation

To start a new conversation with OpenHands to perform a task, you'll need to make a POST request to the conversation endpoint.

#### Request Parameters

| Parameter          | Type     | Required | Description                                                                                          |
|--------------------|----------|----------|------------------------------------------------------------------------------------------------------|
| `initial_user_msg` | string   | Yes      | The initial message to start the conversation.                                                       |
| `repository`       | string   | No       | Git repository name to provide context in the format `owner/repo`. You must have access to the repo. |

#### Examples

<details>
<summary>cURL</summary>

```bash
curl -X POST "https://app.all-hands.dev/api/conversations" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "initial_user_msg": "Check whether there is any incorrect information in the README.md file and send a PR to fix it if so.",
    "repository": "yourusername/your-repo"
  }'
```
</details>

<details>
<summary>Python (with requests)</summary>

```python
import requests

api_key = "YOUR_API_KEY"
url = "https://app.all-hands.dev/api/conversations"

headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

data = {
    "initial_user_msg": "Check whether there is any incorrect information in the README.md file and send a PR to fix it if so.",
    "repository": "yourusername/your-repo"
}

response = requests.post(url, headers=headers, json=data)
conversation = response.json()

print(f"Conversation Link: https://app.all-hands.dev/conversations/{conversation['conversation_id']}")
print(f"Status: {conversation['status']}")
```
</details>

<details>
<summary>TypeScript/JavaScript (with fetch)</summary>

```typescript
const apiKey = "YOUR_API_KEY";
const url = "https://app.all-hands.dev/api/conversations";

const headers = {
  "Authorization": `Bearer ${apiKey}`,
  "Content-Type": "application/json"
};

const data = {
  initial_user_msg: "Check whether there is any incorrect information in the README.md file and send a PR to fix it if so.",
  repository: "yourusername/your-repo"
};

async function startConversation() {
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(data)
    });

    const conversation = await response.json();

    console.log(`Conversation Link: https://app.all-hands.dev/conversations/${conversation.id}`);
    console.log(`Status: ${conversation.status}`);

    return conversation;
  } catch (error) {
    console.error("Error starting conversation:", error);
  }
}

startConversation();
```

</details>

#### Response

The API will return a JSON object with details about the created conversation:

```json
{
  "status": "ok",
  "conversation_id": "abc1234",
}
```

You may receive an `AuthenticationError` if:

- You provided an invalid API key.
- You provided the wrong repository name.
- You don't have access to the repository.


### Retrieving Conversation Status

You can check the status of a conversation by making a GET request to the conversation endpoint.

#### Endpoint

```
GET https://app.all-hands.dev/api/conversations/{conversation_id}
```

#### Example

<details>
<summary>cURL</summary>

```bash
curl -X GET "https://app.all-hands.dev/api/conversations/{conversation_id}" \
  -H "Authorization: Bearer YOUR_API_KEY"
```
</details>

#### Response

The response is formatted as follows:

```json
{
  "conversation_id":"abc1234",
  "title":"Update README.md",
  "created_at":"2025-04-29T15:13:51.370706Z",
  "last_updated_at":"2025-04-29T15:13:57.199210Z",
  "status":"RUNNING",
  "selected_repository":"yourusername/your-repo",
  "trigger":"gui"
}
```

## Rate Limits

If you have too many conversations running at once, older conversations will be paused to limit the number of concurrent conversations.
If you're running into issues and need a higher limit for your use case, please contact us at [<EMAIL>](mailto:<EMAIL>).
