# Openhands Cloud

OpenHands Cloud is the cloud hosted version of OpenHands by All Hands AI.

## Accessing OpenHands Cloud

OpenHands Cloud can be accessed at https://app.all-hands.dev/.

You can also interact with OpenHands Cloud programmatically using the [API](./cloud-api).

## Getting Started

After visiting OpenHands Cloud, you will be asked to connect with your GitHub or GitLab account:

1. After reading and accepting the terms of service, click `Log in with GitHub` or `Log in with GitLab`.
2. Review the permissions requested by OpenHands and then click `Authorize OpenHands AI`.
   - OpenHands will require some permissions from your GitHub or GitLab account. To read more about these permissions:
     - GitHub: You can click the `Learn more` link on the GitHub authorize page.
     - GitLab: You can expand each permission request on the GitLab authorize page.

## Repository Access

### GitHub

#### Adding Repository Access

You can grant OpenHands specific repository access:
1. Click `Add GitHub repos` on the Home page.
2. Select the organization, then choose the specific repositories to grant OpenHands access to.
   <details>
     <summary>Permission Details for Repository Access</summary>

     Openhands requests short-lived tokens (8-hour expiry) with these permissions:
     - Actions: Read and write
     - Administration: Read-only
     - Commit statuses: Read and write
     - Contents: Read and write
     - Issues: Read and write
     - Metadata: Read-only
     - Pull requests: Read and write
     - Webhooks: Read and write
     - Workflows: Read and write

     Repository access for a user is granted based on:
     - Granted permission for the repository.
     - User's GitHub permissions (owner/collaborator).
   </details>

3. Click on `Install & Authorize`.

#### Modifying Repository Access

You can modify GitHub repository access at any time by:
* Using the same `Add GitHub repos` workflow, or
* Visiting the Settings page and selecting `Configure GitHub Repositories` under the `Git Settings` section.

### GitLab

When using your GitLab account, OpenHands will automatically have access to your repositories.

## Conversation Persistence

- Conversations List – Displays only the 20 most recent conversations initiated within the past 10 days.
- Workspaces – Conversation workspaces are retained for 14 days.
- Runtimes – Runtimes remain active ("warm") for 30 minutes. After this period, resuming a conversation may take 1–2 minutes.
