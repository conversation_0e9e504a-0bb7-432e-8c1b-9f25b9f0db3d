# GitLab Installation

This guide walks you through the process of installing and configuring OpenHands Cloud for your GitLab repositories.

## Prerequisites

- A GitLab account
- Access to OpenHands Cloud

## Installation Steps

1. Log in to [OpenHands Cloud](https://app.all-hands.dev)
2. If you haven't connected your GitLab account yet:
   - Click on `Log in with GitLab`
   - Authorize the OpenHands application



## Next Steps

- [Access the Cloud UI](./cloud-ui.md) to interact with the web interface
- [Use the Cloud API](./cloud-api.md) to programmatically interact with OpenHands
