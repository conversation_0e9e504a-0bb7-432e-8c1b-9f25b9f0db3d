# Getting Started with OpenHands

So you've [run OpenHands](./installation) and have
[set up your LLM](./installation#setup). Now what?

OpenHands can assist with a range of engineering tasks. However, the technology is still new, and we’re far from having
agents that can handle complex tasks independently. It’s important to understand what the agent does well and where it
needs support.

## Hello World

Start with a simple "hello world" example. It might be trickier than it seems!

Prompt the agent with:
> Write a bash script hello.sh that prints "hello world!"

The agent will write the script, set the correct permissions, and run it to check the output.

You can continue prompting the agent to refine your code. This is a great way to
work with agents. Start simple, and iterate.

> Modify hello.sh so that it accepts a name as the first argument, but defaults to "world"

You can also use any language you need. The agent may need time to set up the environment.

> Please convert hello.sh to a Ruby script, and run it

## Building From Scratch

Agents excel at "greenfield" tasks, where they don’t need context about existing code and
they can start from scratch.
Begin with a simple task and iterate from there. Be specific about what you want and the tech stack.

For example, we might build a TODO app:

> Build a frontend-only TODO app in React. All state should be stored in localStorage.

Once the basic structure is in place, continue refining:

> Allow adding an optional due date to each task.

Just like normal development, commit and push your code often.
This way you can always revert back to an old state if the agent goes off track.
You can ask the agent to commit and push for you:

> Commit the changes and push them to a new branch called "feature/due-dates"

## Adding New Code

OpenHands is great at adding new code to an existing codebase.

For instance, you can ask OpenHands to add a GitHub action that lints your code. It might check your codebase to
determine the language, then create a new file in `./github/workflows/lint.yml`.

> Add a GitHub action that lints the code in this repository.

Some tasks need more context. While OpenHands can use commands like ls and grep to search, providing context upfront
speeds things up and reduces token usage.

> Modify ./backend/api/routes.js to add a new route that returns a list of all tasks.

> Add a new React component to the ./frontend/components directory to display a list of Widgets.
> It should use the existing Widget component.

## Refactoring

OpenHands does great at refactoring code in small chunks. Rather than rearchitecting the entire codebase,
it's more effective to break up long files and functions or rename variables.

> Rename all the single-letter variables in ./app.go.

> Split the `build_and_deploy_widgets` function into two functions, `build_widgets` and `deploy_widgets` in widget.php.

> Break ./api/routes.js into separate files for each route.

## Bug Fixes

OpenHands can help track down and fix bugs, but bug fixing can be tricky and often requires more context.
It’s helpful if you’ve already diagnosed the issue and just need OpenHands to handle the logic.

> The email field in the `/subscribe` endpoint is rejecting .io domains. Fix this.

> The `search_widgets` function in ./app.py is doing a case-sensitive search. Make it case-insensitive.

For bug fixing, test-driven development can be really useful. You can ask the agent to write a new test and iterate
until the bug is fixed:

> The `hello` function crashes on the empty string. Write a test that reproduces this bug, then fix the code so it passes.

## More

OpenHands can assist with nearly any coding task, but it takes some practice to get the best results.
Keep these tips in mind:
* Keep your tasks small.
* Be specific.
* Provide plenty of context.
* Commit and push frequently.

See [Prompting Best Practices](./prompting/prompting-best-practices) for more tips on how to get the most out of OpenHands.
