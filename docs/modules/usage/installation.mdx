# Running OpenHands

## System Requirements

- MacOS with [Docker Desktop support](https://docs.docker.com/desktop/setup/install/mac-install/#system-requirements)
- Linux
- Windows with [WSL](https://learn.microsoft.com/en-us/windows/wsl/install) and [Docker Desktop support](https://docs.docker.com/desktop/setup/install/windows-install/#system-requirements)
- Windows without WSL (see [Windows Without WSL Guide](./windows-without-wsl))

A system with a modern processor and a minimum of **4GB RAM** is recommended to run OpenHands.

## Prerequisites

<details>
  <summary>MacOS</summary>

  **Docker Desktop**

  1. [Install Docker Desktop on Mac](https://docs.docker.com/desktop/setup/install/mac-install).
  2. Open Docker Desktop, go to `Settings > Advanced` and ensure `Allow the default Docker socket to be used` is enabled.
</details>

<details>
  <summary>Linux</summary>

  :::note
  Tested with Ubuntu 22.04.
  :::

  **Docker Desktop**

  1. [Install Docker Desktop on Linux](https://docs.docker.com/desktop/setup/install/linux/).

</details>

<details>
  <summary>Windows</summary>

  **WSL**

  1. [Install WSL](https://learn.microsoft.com/en-us/windows/wsl/install).
  2. Run `wsl --version` in powershell and confirm `Default Version: 2`.

  **Docker Desktop**

  1. [Install Docker Desktop on Windows](https://docs.docker.com/desktop/setup/install/windows-install).
  2. Open Docker Desktop, go to `Settings` and confirm the following:
  - General: `Use the WSL 2 based engine` is enabled.
  - Resources > WSL Integration: `Enable integration with my default WSL distro` is enabled.

  :::note
  The docker command below to start the app must be run inside the WSL terminal.
  :::

  **Alternative: Windows without WSL**

  If you prefer to run OpenHands on Windows without WSL or Docker, see our [Windows Without WSL Guide](./windows-without-wsl).

</details>

## Start the App

The easiest way to run OpenHands is in Docker.

```bash
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik

docker run -it --rm --pull=always \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik \
    -e LOG_ALL_EVENTS=true \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v ~/.openhands-state:/.openhands-state \
    -p 3000:3000 \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app \
    docker.all-hands.dev/all-hands-ai/openhands:0.39
```

You'll find OpenHands running at http://localhost:3000!

You can also [connect OpenHands to your local filesystem](https://docs.all-hands.dev/modules/usage/runtimes/docker#connecting-to-your-filesystem),
run OpenHands in a scriptable [headless mode](https://docs.all-hands.dev/modules/usage/how-to/headless-mode),
interact with it via a [friendly CLI](https://docs.all-hands.dev/modules/usage/how-to/cli-mode),
or run it on tagged issues with [a GitHub action](https://docs.all-hands.dev/modules/usage/how-to/github-action).

## Setup

After launching OpenHands, you **must** select an `LLM Provider` and `LLM Model` and enter a corresponding `API Key`.
This can be done during the initial settings popup or by selecting the `Settings`
button (gear icon) in the UI.

If the required model does not exist in the list, in `Settings` under the `LLM` tab, you can toggle `Advanced` options
and manually enter it with the correct prefix in the `Custom Model` text box.
The `Advanced` options also allow you to specify a `Base URL` if required.

### Getting an API Key

OpenHands requires an API key to access most language models. Here's how to get an API key from the recommended providers:

#### Anthropic (Claude)

1. [Create an Anthropic account](https://console.anthropic.com/).
2. [Generate an API key](https://console.anthropic.com/settings/keys).
3. [Set up billing](https://console.anthropic.com/settings/billing).

Consider setting usage limits to control costs.

#### OpenAI

1. [Create an OpenAI account](https://platform.openai.com/).
2. [Generate an API key](https://platform.openai.com/api-keys).
3. [Set up billing](https://platform.openai.com/account/billing/overview).

Now you're ready to [get started with OpenHands](./getting-started).

## Versions

The [docker command above](./installation#start-the-app) pulls the most recent stable release of OpenHands. You have other options as well:
- For a specific release, replace $VERSION in `openhands:$VERSION` and `runtime:$VERSION`, with the version number.
We use SemVer so `0.9` will automatically point to the latest `0.9.x` release, and `0` will point to the latest `0.x.x` release.
- For the most up-to-date development version, replace $VERSION in `openhands:$VERSION` and `runtime:$VERSION`, with `main`.
This version is unstable and is recommended for testing or development purposes only.

For the development workflow, see [Development.md](https://github.com/All-Hands-AI/OpenHands/blob/main/Development.md).

Are you having trouble? Check out our [Troubleshooting Guide](https://docs.all-hands.dev/modules/usage/troubleshooting).
