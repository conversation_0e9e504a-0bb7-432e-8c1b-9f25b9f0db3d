# OpenHands Feature Overview

![overview](/img/oh-features.png)

### Chat Panel
- Displays the conversation between the user and OpenHands.
- OpenHands explains its actions in this panel.

### Changes
- Shows the file changes performed by OpenHands.

### VS Code
- Embedded VS Code for browsing and modifying files.
- Can also be used to upload and download files.

### Terminal
- A space for OpenHands and users to run terminal commands.

### Ju<PERSON>ter
- Shows all Python commands that were executed by OpenHands.
- Particularly handy when using OpenHands to perform data visualization tasks.

### App
- Displays the web server when OpenHands runs an application.
- Users can interact with the running application.

### Browser
- Used by OpenHands to browse websites.
- The browser is non-interactive.
