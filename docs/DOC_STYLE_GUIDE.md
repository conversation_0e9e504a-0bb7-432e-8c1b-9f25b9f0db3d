# Documentation Style Guide

## General Writing Principles

- **Clarity & Conciseness**: Always prioritize clarity and brevity. Avoid unnecessary jargon or overly complex explanations.
Keep sentences short and to the point.
- **Gradual Complexity**: Start with the simplest, most basic setup, and then gradually introduce more advanced
concepts and configurations.

## Formatting Guidelines

### Headers

Use **Title Case** for the first and second level headers.

Example:
  - **Basic Usage**
  - **Advanced Configuration Options**

### Lists

When listing items or options, use bullet points to enhance readability.

Example:
  - Option A
  - Option B
  - Option C

### Procedures

For instructions or processes that need to be followed in a specific order, use numbered steps.

Example:
  1. Step one: Do this.
    - First this sub step.
    - Then this sub step.
  2. Step two: Complete this action.
  3. Step three: Verify the result.

### Code Blocks

* Use code blocks for multi-line inputs, outputs, commands and code samples.

Example:
```bash
docker run -it \
    -e THIS=this \
    -e THAT=that
    ...
```

### Use of Note and Warning

When adding a note or warning, use the built-in note and warning syntax.

Example:
:::note
This section is for advanced users only.
:::

### Referring to UI Elements

When referencing UI elements, use ``.

Example:
1. Toggle the `Advanced` option
2. Enter your model in the `Custom Model` textbox.
