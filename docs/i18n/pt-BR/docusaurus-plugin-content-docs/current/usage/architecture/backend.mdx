# 🏛️ Arquitetura do Sistema

<div style={{ textAlign: 'center' }}>
  <img src="https://github.com/All-Hands-AI/OpenHands/assets/16201837/97d747e3-29d8-4ccb-8d34-6ad1adb17f38" alt="Diagrama de Arquitetura do OpenHands 4 de julho de 2024" />
  <p><em>Diagrama de Arquitetura do OpenHands (4 de julho de 2024)</em></p>
</div>

Esta é uma visão geral de alto nível da arquitetura do sistema. O sistema está dividido em dois componentes principais: o frontend e o backend. O frontend é responsável por lidar com as interações do usuário e exibir os resultados. O backend é responsável por gerenciar a lógica de negócios e executar os agentes.

# Arquitetura do Frontend {#frontend-architecture-en}

![system_architecture.svg](/img/system_architecture.svg)

Esta visão geral é simplificada para mostrar os principais componentes e suas interações. Para uma visão mais detalhada da arquitetura do backend, consulte a seção Arquitetura do Backend abaixo.

# Arquitetura do Backend {#backend-architecture-en}

_**Aviso**: A arquitetura do backend é um trabalho em andamento e está sujeita a alterações. O diagrama a seguir mostra a arquitetura atual do backend com base no commit mostrado no rodapé do diagrama._

![backend_architecture.svg](/img/backend_architecture.svg)

<details>
  <summary>Atualizando este Diagrama</summary>
  <div>
    A geração do diagrama de arquitetura do backend é parcialmente automatizada.
    O diagrama é gerado a partir das dicas de tipo no código usando a
    ferramenta py2puml. O diagrama é então revisado manualmente, ajustado e exportado para PNG
    e SVG.

    ## Pré-requisitos

    - Ambiente Python em execução no qual o openhands é executável
    (de acordo com as instruções no arquivo README.md na raiz do repositório)
    - [py2puml](https://github.com/lucsorel/py2puml) instalado

## Passos

1.  Gere automaticamente o diagrama executando o seguinte comando a partir da raiz do repositório:
    `py2puml openhands openhands > docs/architecture/backend_architecture.puml`

2.  Abra o arquivo gerado em um editor PlantUML, por exemplo, Visual Studio Code com a extensão PlantUML ou [PlantText](https://www.planttext.com/)

3.  Revise o PUML gerado e faça todos os ajustes necessários ao diagrama (adicione partes ausentes, corrija erros, melhore o posicionamento).
    _py2puml cria o diagrama com base nas dicas de tipo no código, portanto, dicas de tipo ausentes ou incorretas podem resultar em um diagrama incompleto ou incorreto._

4.  Revise a diferença entre o novo diagrama e o anterior e verifique manualmente se as alterações estão corretas.
    _Certifique-se de não remover partes que foram adicionadas manualmente ao diagrama no passado e ainda são relevantes._

5.  Adicione o hash do commit que foi usado para gerar o diagrama ao rodapé do diagrama.

6.  Exporte o diagrama como arquivos PNG e SVG e substitua os diagramas existentes no diretório `docs/architecture`. Isso pode ser feito com (por exemplo, [PlantText](https://www.planttext.com/))

  </div>
</details>
