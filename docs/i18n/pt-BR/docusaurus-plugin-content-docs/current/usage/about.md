# Sobre o OpenHands

## Estratégia de Pesquisa

Alcançar a replicação completa de aplicações de nível de produção com LLMs é um empreendimento complexo. Nossa estratégia envolve:

- **Pesquisa Técnica Fundamental:** Foco em pesquisa fundamental para entender e melhorar os aspectos técnicos de geração e manipulação de código.
- **Planejamento de Tarefas:** Desenvolvimento de capacidades para detecção de bugs, gerenciamento de base de código e otimização.
- **Avaliação:** Estabelecimento de métricas de avaliação abrangentes para melhor entender e aprimorar nossos agentes.

## Agente Padrão

Nosso Agente padrão é atualmente o [CodeActAgent](agents), que é capaz de gerar código e manipular arquivos.

## Construído Com

O OpenHands é construído usando uma combinação de frameworks e bibliotecas poderosas, fornecendo uma base robusta para seu desenvolvimento. Aqui estão as principais tecnologias utilizadas no projeto:

![FastAPI](https://img.shields.io/badge/FastAPI-black?style=for-the-badge) ![uvicorn](https://img.shields.io/badge/uvicorn-black?style=for-the-badge) ![LiteLLM](https://img.shields.io/badge/LiteLLM-black?style=for-the-badge) ![Docker](https://img.shields.io/badge/Docker-black?style=for-the-badge) ![Ruff](https://img.shields.io/badge/Ruff-black?style=for-the-badge) ![MyPy](https://img.shields.io/badge/MyPy-black?style=for-the-badge) ![LlamaIndex](https://img.shields.io/badge/LlamaIndex-black?style=for-the-badge) ![React](https://img.shields.io/badge/React-black?style=for-the-badge)

Por favor, note que a seleção dessas tecnologias está em andamento, e tecnologias adicionais podem ser adicionadas ou as existentes podem ser removidas conforme o projeto evolui. Nós nos esforçamos para adotar as ferramentas mais adequadas e eficientes para aprimorar as capacidades do OpenHands.

## Licença

Distribuído sob a [Licença](https://github.com/All-Hands-AI/OpenHands/blob/main/LICENSE) MIT.
