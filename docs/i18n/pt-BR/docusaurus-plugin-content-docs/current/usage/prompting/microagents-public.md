# Microagentes Globais

## Visão Geral

Microagentes globais são [microagentes ativados por palavras-chave](./microagents-keyword) que se aplicam a todos os usuários do OpenHands. Uma lista dos microagentes globais atuais pode ser encontrada [no repositório OpenHands](https://github.com/All-Hands-AI/OpenHands/tree/main/microagents).

## Contribuindo com um Microagente Global

Você pode criar microagentes globais e compartilhá-los com a comunidade abrindo um pull request no repositório oficial.

Consulte o [CONTRIBUTING.md](https://github.com/All-Hands-AI/OpenHands/blob/main/CONTRIBUTING.md) para instruções específicas sobre como contribuir para o OpenHands.

### Melhores Práticas para Microagentes Globais

- **Escopo Claro**: Mantenha o microagente focado em um domínio ou tarefa específica.
- **Instruções Explícitas**: Forneça diretrizes claras e não ambíguas.
- **Exemplos Úteis**: Inclua exemplos práticos de casos de uso comuns.
- **Segurança em Primeiro Lugar**: Inclua avisos e restrições necessários.
- **Consciência de Integração**: Considere como o microagente interage com outros componentes.

### Passos para Contribuir com um Microagente Global

#### 1. Planeje o Microagente Global

Antes de criar um microagente global, considere:

- Qual problema específico ou caso de uso ele abordará?
- Quais capacidades ou conhecimentos únicos ele deve ter?
- Quais palavras-gatilho fazem sentido para ativá-lo?
- Quais restrições ou diretrizes ele deve seguir?

#### 2. Crie o Arquivo

Crie um novo arquivo Markdown com um nome descritivo no diretório apropriado:
[`microagents/`](https://github.com/All-Hands-AI/OpenHands/tree/main/microagents)

#### 3. Testando o Microagente Global

- Teste o agente com vários prompts.
- Verifique se as palavras-gatilho ativam o agente corretamente.
- Certifique-se de que as instruções sejam claras e abrangentes.
- Verifique possíveis conflitos e sobreposições com agentes existentes.

#### 4. Processo de Submissão

Envie um pull request com:

- O novo arquivo do microagente.
- Documentação atualizada, se necessário.
- Descrição do propósito e capacidades do agente.
