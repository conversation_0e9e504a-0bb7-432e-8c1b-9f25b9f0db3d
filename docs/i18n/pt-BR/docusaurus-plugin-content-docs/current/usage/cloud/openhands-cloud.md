# OpenHands Cloud

OpenHands Cloud é a versão hospedada na nuvem do OpenHands da All Hands AI.

## Acessando o OpenHands Cloud

Para começar com o OpenHands Cloud, visite [app.all-hands.dev](https://app.all-hands.dev).

Você será solicitado a se conectar com sua conta GitHub ou GitLab:

1. Após ler e aceitar os termos de serviço, clique em `Conectar ao GitHub` ou `Conectar ao GitLab`.
2. Revise as permissões solicitadas pelo OpenHands e autorize a aplicação.
   - OpenHands exigirá certas permissões da sua conta. Para ler mais sobre essas permissões,
     você pode clicar no link `Saiba mais` na página de autorização.

## Próximos Passos

Depois de conectar sua conta, você pode:

- [Instalar a Integração com GitHub](./github-installation.md) para usar o OpenHands com seus repositórios GitHub
- [Instalar a Integração com GitLab](./gitlab-installation.md) para usar o OpenHands com seus repositórios GitLab
- [Acessar a Interface da Nuvem](./cloud-ui.md) para interagir com a interface web
- [Usar a API da Nuvem](./cloud-api.md) para interagir programaticamente com o OpenHands
- [Configurar o Resolvedor de Problemas na Nuvem](./cloud-issue-resolver.md) para automatizar correções de código e fornecer assistência inteligente
