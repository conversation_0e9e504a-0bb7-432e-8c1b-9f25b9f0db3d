# Visão Geral das Funcionalidades do OpenHands

![visão geral](/img/oh-features.png)

### Painel de Chat
- Exibe a conversa entre o usuário e o OpenHands.
- O OpenHands explica suas ações neste painel.

### Alterações
- <PERSON>ra as alterações de arquivos realizadas pelo OpenHands.

### VS Code
- VS Code incorporado para navegar e modificar arquivos.
- Também pode ser usado para fazer upload e download de arquivos.

### Terminal
- Um espaço para o OpenHands e usuários executarem comandos de terminal.

### Jupyter
- Mostra todos os comandos Python que foram executados pelo OpenHands.
- Particularmente útil ao usar o OpenHands para tarefas de visualização de dados.

### Aplicativo
- Exibe o servidor web quando o OpenHands executa uma aplicação.
- Os usuários podem interagir com a aplicação em execução.

### Navegador
- Usado pelo OpenHands para navegar em sites.
- O navegador não é interativo.
