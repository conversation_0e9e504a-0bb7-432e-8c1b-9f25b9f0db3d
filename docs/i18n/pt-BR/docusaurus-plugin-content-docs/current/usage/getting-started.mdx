# Começando com OpenHands

Então você já [executou o OpenHands](./installation) e
[configurou seu LLM](./installation#setup). E agora?

O OpenHands pode ajudar com uma variedade de tarefas de engenharia. No entanto, a tecnologia ainda é nova, e estamos longe de ter
agentes que possam lidar com tarefas complexas de forma independente. É importante entender o que o agente faz bem e onde ele
precisa de suporte.

## Hello World

Comece com um exemplo simples de "hello world". Pode ser mais complicado do que parece!

Solicite ao agente com:
> Escreva um script bash hello.sh que imprima "hello world!"

O agente escreverá o script, definirá as permissões corretas e o executará para verificar a saída.

Você pode continuar solicitando ao agente para refinar seu código. Esta é uma ótima maneira de
trabalhar com agentes. Comece de forma simples e vá iterando.

> Modifique hello.sh para que aceite um nome como primeiro argumento, mas use "world" como padrão

Você também pode usar qualquer linguagem que precisar. O agente pode precisar de tempo para configurar o ambiente.

> Por favor, converta hello.sh para um script Ruby e execute-o

## Construindo do Zero

Os agentes se destacam em tarefas "greenfield", onde não precisam de contexto sobre código existente e
podem começar do zero.
Comece com uma tarefa simples e itere a partir daí. Seja específico sobre o que você quer e a stack tecnológica.

Por exemplo, podemos construir um aplicativo de tarefas:

> Construa um aplicativo de tarefas apenas com frontend em React. Todo o estado deve ser armazenado no localStorage.

Uma vez que a estrutura básica esteja pronta, continue refinando:

> Permita adicionar uma data de vencimento opcional a cada tarefa.

Assim como no desenvolvimento normal, faça commit e push do seu código com frequência.
Dessa forma, você sempre pode reverter para um estado anterior se o agente se desviar.
Você pode pedir ao agente para fazer commit e push para você:

> Faça commit das alterações e envie-as para uma nova branch chamada "feature/due-dates"

## Adicionando Novo Código

O OpenHands é ótimo para adicionar novo código a uma base de código existente.

Por exemplo, você pode pedir ao OpenHands para adicionar uma ação do GitHub que faça lint do seu código. Ele pode verificar sua base de código para
determinar a linguagem e, em seguida, criar um novo arquivo em `./github/workflows/lint.yml`.

> Adicione uma ação do GitHub que faça lint do código neste repositório.

Algumas tarefas precisam de mais contexto. Embora o OpenHands possa usar comandos como ls e grep para pesquisar, fornecer contexto antecipadamente
acelera as coisas e reduz o uso de tokens.

> Modifique ./backend/api/routes.js para adicionar uma nova rota que retorne uma lista de todas as tarefas.

> Adicione um novo componente React ao diretório ./frontend/components para exibir uma lista de Widgets.
> Ele deve usar o componente Widget existente.

## Refatoração

O OpenHands é ótimo para refatorar código em pequenos pedaços. Em vez de reconfigurar toda a base de código,
é mais eficaz dividir arquivos e funções longas ou renomear variáveis.

> Renomeie todas as variáveis de letra única em ./app.go.

> Divida a função `build_and_deploy_widgets` em duas funções, `build_widgets` e `deploy_widgets` em widget.php.

> Divida ./api/routes.js em arquivos separados para cada rota.

## Correção de Bugs

O OpenHands pode ajudar a rastrear e corrigir bugs, mas a correção de bugs pode ser complicada e geralmente requer mais contexto.
É útil se você já diagnosticou o problema e só precisa que o OpenHands lide com a lógica.

> O campo de email no endpoint `/subscribe` está rejeitando domínios .io. Corrija isso.

> A função `search_widgets` em ./app.py está fazendo uma busca sensível a maiúsculas e minúsculas. Torne-a insensível a maiúsculas e minúsculas.

Para correção de bugs, o desenvolvimento orientado a testes pode ser muito útil. Você pode pedir ao agente para escrever um novo teste e iterar
até que o bug seja corrigido:

> A função `hello` falha com uma string vazia. Escreva um teste que reproduza esse bug e, em seguida, corrija o código para que ele passe.

## Mais

O OpenHands pode ajudar com quase qualquer tarefa de codificação, mas é preciso alguma prática para obter os melhores resultados.
Tenha em mente estas dicas:
* Mantenha suas tarefas pequenas.
* Seja específico.
* Forneça bastante contexto.
* Faça commit e push com frequência.

Veja [Melhores Práticas de Prompt](./prompting/prompting-best-practices) para mais dicas sobre como obter o máximo do OpenHands.
