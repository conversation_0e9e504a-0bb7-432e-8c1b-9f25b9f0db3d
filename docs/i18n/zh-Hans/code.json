{"footer.title": {"message": "OpenHands"}, "footer.docs": {"message": "文档"}, "footer.community": {"message": "社区"}, "footer.copyright": {"message": "版权所有 © {year} OpenHands"}, "faq.title": {"message": "常见问题解答", "description": "FAQ Title"}, "faq.description": {"message": "常见问题解答"}, "faq.section.title.1": {"message": "什么是OpenHands？", "description": "First Section Title"}, "faq.section.highlight": {"message": "OpenHands", "description": "Highlight Text"}, "faq.section.description.1": {"message": "是一个自主的软件工程师，能够端到端地解决软件工程和网页浏览任务。它能执行数据科学查询，如 \"查找上个月OpenHands仓库中的拉取请求数量\"，还能处理软件工程任务，例如 \"请为这个文件添加测试并验证所有测试都通过，如果没有修复该文件\"。", "description": "Description for OpenHands"}, "faq.section.description.2": {"message": "同时，OpenHands是一个代理开发者平台和社区，用于测试和评估新代理的环境。", "description": "Further Description for OpenHands"}, "faq.section.title.2": {"message": "支持", "description": "Support Section Title"}, "faq.section.support.answer": {"message": "如果您发现了可能影响他人的问题，请在 {githubLink} 上提交一个 bug。如果遇到安装困难或有其他疑问，可以访问 {discordLink} 或 {slackLink} 进行提问。", "description": "Support Answer"}, "faq.section.title.3": {"message": "如何使用OpenHands解决GitHub上的问题？", "description": "GitHub Issue Section Title"}, "faq.section.github.steps.intro": {"message": "要通过OpenHands解决GitHub上的问题，您可以发送一个提示给OpenHands，请它按照以下步骤操作：", "description": "GitHub Steps Introduction"}, "faq.section.github.step1": {"message": "阅读问题 https://github.com/All-Hands-AI/OpenHands/issues/1611", "description": "GitHub Step 1"}, "faq.section.github.step2": {"message": "克隆仓库并创建新分支", "description": "GitHub Step 2"}, "faq.section.github.step3": {"message": "根据问题描述中的说明，修改文件以解决问题", "description": "GitHub Step 3"}, "faq.section.github.step4": {"message": "使用GITHUB_TOKEN环境变量将结果推送到GitHub", "description": "GitHub Step 4"}, "faq.section.github.step5": {"message": "告诉我需要前往的链接来提交拉取请求", "description": "GitHub Step 5"}, "faq.section.github.steps.preRun": {"message": "在运行OpenHands之前，您可以：", "description": "GitHub Steps Pre-Run"}, "faq.section.github.steps.tokenInfo": {"message": "其中XXX是您创建的一个具有对OpenHands仓库写权限的GitHub令牌。如果您的写入权限不足，请将其更改为：", "description": "GitHub Steps Token Info"}, "faq.section.github.steps.usernameInfo": {"message": "其中USERNAME是您的GitHub用户名。", "description": "GitHub Steps Username Info"}, "faq.section.title.4": {"message": "OpenHands与Devin有何不同？", "description": "Devin Section Title"}, "faq.section.openhands.linkText": {"message": "<PERSON>", "description": "<PERSON>"}, "faq.section.openhands.description": {"message": "是由Cognition Inc.开发的商业产品，它最初为OpenHands提供了灵感。它们都旨在擅长解决软件工程任务，但您可以下载、使用和修改OpenHands，而Devin只能通过Cognition网站进行访问。此外，OpenHands已超越最初的灵感，并成为一个面向代理开发者的社区驱动生态系统，在这里我们欢迎您加入并", "description": "Devin <PERSON>"}, "faq.section.openhands.contribute": {"message": "贡献", "description": "Contribute Link"}, "faq.section.title.5": {"message": "OpenHands与ChatGPT有何不同？", "description": "ChatGPT Section Title"}, "faq.section.chatgpt.description": {"message": "您可以通过网络访问ChatGPT，它不与本地文件交互，并且其执行代码的能力有限。因此，它可以编写代码，但测试或执行起来可能不太容易。", "description": "ChatGPT Description"}, "homepage.description": {"message": "使用AI生成代码的软件工程工具。", "description": "The homepage description"}, "homepage.getStarted": {"message": "开始使用"}, "welcome.message": {"message": "欢迎来到OpenHands，这是一个开源自主AI软件工程师，能够执行复杂的工程任务，并积极参与用户在软件开发项目中的协作。"}, "theme.ErrorPageContent.title": {"message": "页面已崩溃。", "description": "The title of the fallback page when the page crashed"}, "theme.BackToTopButton.buttonAriaLabel": {"message": "返回顶部", "description": "The ARIA label for the back to top button"}, "theme.blog.archive.title": {"message": "历史博文", "description": "The page & hero title of the blog archive page"}, "theme.blog.archive.description": {"message": "历史博文", "description": "The page & hero description of the blog archive page"}, "theme.blog.paginator.navAriaLabel": {"message": "博文列表分页导航", "description": "The ARIA label for the blog pagination"}, "theme.blog.paginator.newerEntries": {"message": "较新的博文", "description": "The label used to navigate to the newer blog posts page (previous page)"}, "theme.blog.paginator.olderEntries": {"message": "较旧的博文", "description": "The label used to navigate to the older blog posts page (next page)"}, "theme.blog.post.paginator.navAriaLabel": {"message": "博文分页导航", "description": "The ARIA label for the blog posts pagination"}, "theme.blog.post.paginator.newerPost": {"message": "较新一篇", "description": "The blog post button label to navigate to the newer/previous post"}, "theme.blog.post.paginator.olderPost": {"message": "较旧一篇", "description": "The blog post button label to navigate to the older/next post"}, "theme.blog.post.plurals": {"message": "{count} 篇博文", "description": "Pluralized label for \"{count} posts\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.tagTitle": {"message": "{nPosts} 含有标签「{tagName}」", "description": "The title of the page for a blog tag"}, "theme.tags.tagsPageLink": {"message": "查看所有标签", "description": "The label of the link targeting the tag list page"}, "theme.colorToggle.ariaLabel": {"message": "切换浅色/暗黑模式（当前为{mode}）", "description": "The ARIA label for the navbar color mode toggle"}, "theme.colorToggle.ariaLabel.mode.dark": {"message": "暗黑模式", "description": "The name for the dark color mode"}, "theme.colorToggle.ariaLabel.mode.light": {"message": "浅色模式", "description": "The name for the light color mode"}, "theme.docs.breadcrumbs.navAriaLabel": {"message": "页面路径", "description": "The ARIA label for the breadcrumbs"}, "theme.docs.DocCard.categoryDescription.plurals": {"message": "{count} 个项目", "description": "The default description for a category card in the generated index about how many items this category includes"}, "theme.docs.paginator.navAriaLabel": {"message": "文件选项卡", "description": "The ARIA label for the docs pagination"}, "theme.docs.paginator.previous": {"message": "上一页", "description": "The label used to navigate to the previous doc"}, "theme.docs.paginator.next": {"message": "下一页", "description": "The label used to navigate to the next doc"}, "theme.docs.tagDocListPageTitle.nDocsTagged": {"message": "{count} 篇文档带有标签", "description": "Pluralized label for \"{count} docs tagged\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.tagDocListPageTitle": {"message": "{nDocsTagged}「{tagName}」", "description": "The title of the page for a docs tag"}, "theme.docs.versionBadge.label": {"message": "版本：{versionLabel}"}, "theme.docs.versions.unreleasedVersionLabel": {"message": "此为 {siteTitle} {versionLabel} 版尚未发行的文档。", "description": "The label used to tell the user that he's browsing an unreleased doc version"}, "theme.docs.versions.unmaintainedVersionLabel": {"message": "此为 {siteTitle} {versionLabel} 版的文档，现已不再积极维护。", "description": "The label used to tell the user that he's browsing an unmaintained doc version"}, "theme.docs.versions.latestVersionSuggestionLabel": {"message": "最新的文档请参阅 {latestVersionLink} ({versionLabel})。", "description": "The label used to tell the user to check the latest version"}, "theme.docs.versions.latestVersionLinkLabel": {"message": "最新版本", "description": "The label used for the latest version suggestion link label"}, "theme.common.editThisPage": {"message": "编辑此页", "description": "The link label to edit the current page"}, "theme.common.headingLinkTitle": {"message": "{heading}的直接链接", "description": "Title for link to heading"}, "theme.lastUpdated.atDate": {"message": "于 {date} ", "description": "The words used to describe on which date a page has been last updated"}, "theme.lastUpdated.byUser": {"message": "由 {user} ", "description": "The words used to describe by who the page has been last updated"}, "theme.lastUpdated.lastUpdatedAtBy": {"message": "最后{byUser}{atDate}更新", "description": "The sentence used to display when a page has been last updated, and by who"}, "theme.navbar.mobileVersionsDropdown.label": {"message": "选择版本", "description": "The label for the navbar versions dropdown on mobile view"}, "theme.NotFound.title": {"message": "找不到页面", "description": "The title of the 404 page"}, "theme.tags.tagsListLabel": {"message": "标签：", "description": "The label alongside a tag list"}, "theme.admonition.caution": {"message": "警告", "description": "The default label used for the Caution admonition (:::caution)"}, "theme.admonition.danger": {"message": "危险", "description": "The default label used for the Danger admonition (:::danger)"}, "theme.admonition.info": {"message": "信息", "description": "The default label used for the Info admonition (:::info)"}, "theme.admonition.note": {"message": "备注", "description": "The default label used for the Note admonition (:::note)"}, "theme.admonition.tip": {"message": "提示", "description": "The default label used for the Tip admonition (:::tip)"}, "theme.admonition.warning": {"message": "注意", "description": "The default label used for the Warning admonition (:::warning)"}, "theme.AnnouncementBar.closeButtonAriaLabel": {"message": "关闭", "description": "The ARIA label for close button of announcement bar"}, "theme.blog.sidebar.navAriaLabel": {"message": "最近博文导航", "description": "The ARIA label for recent posts in the blog sidebar"}, "theme.CodeBlock.copied": {"message": "复制成功", "description": "The copied button label on code blocks"}, "theme.CodeBlock.copyButtonAriaLabel": {"message": "将代码复制到剪贴板", "description": "The ARIA label for copy code blocks button"}, "theme.CodeBlock.copy": {"message": "复制", "description": "The copy button label on code blocks"}, "theme.CodeBlock.wordWrapToggle": {"message": "切换自动换行", "description": "The title attribute for toggle word wrapping button of code block lines"}, "theme.DocSidebarItem.expandCategoryAriaLabel": {"message": "展开侧边栏分类 '{label}'", "description": "The ARIA label to expand the sidebar category"}, "theme.DocSidebarItem.collapseCategoryAriaLabel": {"message": "折叠侧边栏分类 '{label}'", "description": "The ARIA label to collapse the sidebar category"}, "theme.NavBar.navAriaLabel": {"message": "主导航", "description": "The ARIA label for the main navigation"}, "theme.navbar.mobileLanguageDropdown.label": {"message": "选择语言", "description": "The label for the mobile language switcher dropdown"}, "theme.NotFound.p1": {"message": "我们找不到您要找的页面。", "description": "The first paragraph of the 404 page"}, "theme.NotFound.p2": {"message": "请联系原始链接来源网站的所有者，并告知他们链接已损坏。", "description": "The 2nd paragraph of the 404 page"}, "theme.TOCCollapsible.toggleButtonLabel": {"message": "本页总览", "description": "The label used by the button on the collapsible TOC component"}, "theme.blog.post.readMore": {"message": "阅读更多", "description": "The label used in blog post item excerpts to link to full blog posts"}, "theme.blog.post.readMoreLabel": {"message": "阅读 {title} 的全文", "description": "The ARIA label for the link to full blog posts from excerpts"}, "theme.blog.post.readingTime.plurals": {"message": "阅读需 {readingTime} 分钟", "description": "Pluralized label for \"{readingTime} min read\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.breadcrumbs.home": {"message": "主页面", "description": "The ARIA label for the home page in the breadcrumbs"}, "theme.docs.sidebar.collapseButtonTitle": {"message": "收起侧边栏", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.collapseButtonAriaLabel": {"message": "收起侧边栏", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.navAriaLabel": {"message": "文档侧边栏", "description": "The ARIA label for the sidebar navigation"}, "theme.docs.sidebar.closeSidebarButtonAriaLabel": {"message": "关闭导航栏", "description": "The ARIA label for close button of mobile sidebar"}, "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": {"message": "← 回到主菜单", "description": "The label of the back button to return to main menu, inside the mobile navbar sidebar secondary menu (notably used to display the docs sidebar)"}, "theme.docs.sidebar.toggleSidebarButtonAriaLabel": {"message": "切换导航栏", "description": "The ARIA label for hamburger menu button of mobile navigation"}, "theme.docs.sidebar.expandButtonTitle": {"message": "展开侧边栏", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.expandButtonAriaLabel": {"message": "展开侧边栏", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.ErrorPageContent.tryAgain": {"message": "重试", "description": "The label of the button to try again rendering when the React error boundary captures an error"}, "theme.common.skipToMainContent": {"message": "跳到主要内容", "description": "The skip to content label used for accessibility, allowing to rapidly navigate to main content with keyboard tab/enter navigation"}, "theme.tags.tagsPageTitle": {"message": "标签", "description": "The title of the tag list page"}, "theme.unlistedContent.title": {"message": "未列出页", "description": "The unlisted content banner title"}, "theme.unlistedContent.message": {"message": "此页面未列出。搜索引擎不会对其索引，只有拥有直接链接的用户才能访问。", "description": "The unlisted content banner message"}, "Use AI to tackle the toil in your backlog. Our agents have all the same tools as a human developer: they can modify code, run commands, browse the web, call APIs, and yes-even copy code snippets from StackOverflow.": {"message": "使用AI处理您积压的工作。我们的代理拥有与人类开发者相同的工具：它们可以修改代码、运行命令、浏览网页、调用API，甚至从StackOverflow复制代码片段。"}, "Get started with OpenHands.": {"message": "开始使用OpenHands"}, "Most Popular Links": {"message": "热门链接"}, "Customizing OpenHands to a repository": {"message": "为仓库定制OpenHands"}, "Integrating OpenHands with Github": {"message": "将OpenHands与Github集成"}, "Recommended models to use": {"message": "推荐使用的模型"}, "Connecting OpenHands to your filesystem": {"message": "将OpenHands连接到您的文件系统"}}