# OpenHands 入门指南

您已经[运行了 OpenHands](./installation)并且[设置了您的 LLM](./installation#setup)。接下来做什么？

OpenHands 可以协助完成各种工程任务。然而，这项技术仍然很新，我们距离拥有能够独立处理复杂任务的代理还有很长的路要走。了解代理擅长什么以及在哪些方面需要支持是很重要的。

## Hello World

从一个简单的"hello world"示例开始。这可能比看起来更棘手！

向代理提示：
> 编写一个 bash 脚本 hello.sh，打印"hello world!"

代理将编写脚本，设置正确的权限，并运行它以检查输出。

您可以继续提示代理来完善您的代码。这是与代理合作的好方法。从简单开始，然后迭代。

> 修改 hello.sh，使其接受名称作为第一个参数，但默认为"world"

您也可以使用任何需要的语言。代理可能需要时间来设置环境。

> 请将 hello.sh 转换为 Ruby 脚本，并运行它

## 从头开始构建

代理在"绿地"任务中表现出色，在这些任务中，它们不需要了解现有代码的上下文，可以从头开始。
从一个简单的任务开始，然后从那里迭代。明确说明您想要什么和技术栈。

例如，我们可以构建一个 TODO 应用：

> 用 React 构建一个仅前端的 TODO 应用。所有状态都应存储在 localStorage 中。

一旦基本结构就位，继续完善：

> 允许为每个任务添加可选的截止日期。

就像正常开发一样，经常提交和推送您的代码。
这样，如果代理偏离轨道，您总是可以恢复到旧状态。
您可以要求代理为您提交和推送：

> 提交更改并将其推送到名为"feature/due-dates"的新分支

## 添加新代码

OpenHands 非常擅长向现有代码库添加新代码。

例如，您可以要求 OpenHands 添加一个对代码进行 lint 检查的 GitHub action。它可能会检查您的代码库以确定语言，然后在 `./github/workflows/lint.yml` 中创建一个新文件。

> 添加一个对此存储库中的代码进行 lint 检查的 GitHub action。

有些任务需要更多上下文。虽然 OpenHands 可以使用 ls 和 grep 等命令进行搜索，但提前提供上下文可以加快速度并减少令牌使用。

> 修改 ./backend/api/routes.js 以添加一个返回所有任务列表的新路由。

> 在 ./frontend/components 目录中添加一个新的 React 组件来显示 Widget 列表。
> 它应该使用现有的 Widget 组件。

## 重构

OpenHands 擅长小块重构代码。与其重新架构整个代码库，更有效的方法是拆分长文件和函数或重命名变量。

> 重命名 ./app.go 中所有的单字母变量。

> 将 widget.php 中的 `build_and_deploy_widgets` 函数拆分为两个函数，`build_widgets` 和 `deploy_widgets`。

> 将 ./api/routes.js 拆分为每个路由的单独文件。

## 错误修复

OpenHands 可以帮助追踪和修复错误，但错误修复可能很棘手，通常需要更多上下文。
如果您已经诊断出问题，只需要 OpenHands 处理逻辑，这会很有帮助。

> `/subscribe` 端点中的电子邮件字段拒绝 .io 域名。修复这个问题。

> ./app.py 中的 `search_widgets` 函数正在执行区分大小写的搜索。使其不区分大小写。

对于错误修复，测试驱动开发非常有用。您可以要求代理编写一个新测试，然后迭代直到错误被修复：

> `hello` 函数在空字符串上崩溃。编写一个重现此错误的测试，然后修复代码使其通过。

## 更多

OpenHands 几乎可以协助任何编码任务，但需要一些实践才能获得最佳结果。
请记住以下提示：
* 保持任务小型化。
* 具体明确。
* 提供充分的上下文。
* 频繁提交和推送。

查看[提示最佳实践](./prompting/prompting-best-practices)获取更多关于如何充分利用 OpenHands 的提示。
