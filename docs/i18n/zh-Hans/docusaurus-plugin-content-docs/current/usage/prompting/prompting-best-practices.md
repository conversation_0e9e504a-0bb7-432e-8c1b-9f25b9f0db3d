# 提示最佳实践

在使用OpenHands AI软件开发者时，提供清晰有效的提示是获得准确且有用回应的关键。本指南概述了制定有效提示的最佳实践。

## 良好提示的特征

好的提示是：

- **具体的**：清楚描述应该添加什么功能或需要修复什么错误。
- **位置明确的**：如果已知，指明代码库中应该修改的位置。
- **范围适当的**：专注于单一功能，通常不超过100行代码。

## 示例

### 好的提示示例

- 在`utils/math_operations.py`中添加一个函数`calculate_average`，该函数接受一个数字列表作为输入并返回它们的平均值。
- 修复`frontend/src/components/UserProfile.tsx`第42行出现的TypeError。错误表明我们试图访问undefined的属性。
- 为注册表单中的电子邮件字段实现输入验证。更新`frontend/src/components/RegistrationForm.tsx`，在提交前检查电子邮件是否为有效格式。

### 不好的提示示例

- 让代码更好。（太模糊，不具体）
- 重写整个后端以使用不同的框架。（范围不适当）
- 用户认证中某处有一个错误。你能找到并修复它吗？（缺乏特异性和位置信息）

## 有效提示的技巧

- 尽可能具体地描述期望的结果或要解决的问题。
- 提供上下文，包括相关文件路径和行号（如果可用）。
- 将大任务分解为更小、更易管理的提示。
- 包含相关的错误消息或日志。
- 如果不明显，指定编程语言或框架。

您的提示越精确和信息丰富，OpenHands就能提供越好的帮助。

查看[OpenHands入门](../getting-started)获取更多有用提示的示例。
