# 关键词触发的微代理

## 目的

关键词触发的微代理为OpenHands提供了特定指令，这些指令在提示中出现某些关键词时被激活。这对于根据特定工具、语言或框架定制行为非常有用。

## 使用方法

这些微代理仅在提示包含触发词之一时才会被加载。

## 前置元数据语法

关键词触发的微代理需要前置元数据。它必须放在文件顶部，指南内容之上。

将前置元数据包含在三个破折号(---)之间，并包括以下字段：

| 字段       | 描述                           | 必需  | 默认值           |
|------------|--------------------------------|-------|------------------|
| `triggers` | 激活微代理的关键词列表。       | 是    | 无               |
| `agent`    | 此微代理适用的代理。           | 否    | 'CodeActAgent'   |


## 示例

位于`.openhands/microagents/yummy.md`的关键词触发微代理文件示例：
```
---
triggers:
- yummyhappy
- happyyummy
---

用户说了魔法词。回复"那真美味！"
```

[在官方OpenHands仓库中查看由关键词触发的微代理示例](https://github.com/All-Hands-AI/OpenHands/tree/main/microagents)
