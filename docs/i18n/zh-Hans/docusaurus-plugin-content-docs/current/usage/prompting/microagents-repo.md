# 通用仓库微代理

## 目的

为OpenHands提供更有效地与仓库协作的一般性指南。

## 用法

这些微代理始终作为上下文的一部分被加载。

## 前置元数据语法

这类微代理的前置元数据是可选的。

前置元数据应该被三个短横线(---)包围，可能包含以下字段：

| 字段      | 描述                           | 必需 | 默认值         |
|-----------|--------------------------------|------|----------------|
| `agent`   | 此微代理适用的代理             | 否   | 'CodeActAgent' |

## 示例

位于`.openhands/microagents/repo.md`的通用仓库微代理文件示例：
```
这个项目是一个TODO应用程序，允许用户跟踪TODO项目。

要设置它，你可以运行`npm run build`。
在提交更改之前，始终确保测试通过。你可以通过运行`npm run test`来执行测试。
```

[在这里查看更多通用仓库微代理的示例。](https://github.com/All-Hands-AI/OpenHands/tree/main/.openhands/microagents)
