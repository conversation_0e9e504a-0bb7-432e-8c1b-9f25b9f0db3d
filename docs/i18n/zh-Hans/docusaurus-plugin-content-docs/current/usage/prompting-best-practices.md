以下是翻译后的内容:

# 提示最佳实践

在使用 OpenHands AI 软件开发者时,提供清晰有效的提示至关重要。本指南概述了创建提示的最佳实践,以产生最准确和最有用的响应。

## 好的提示的特点

好的提示是:

1. **具体的**: 它们准确解释应该添加什么功能或需要修复什么错误。
2. **位置特定的**: 如果已知,它们解释了应该修改代码库中的哪些位置。
3. **适当范围的**: 它们应该是单个功能的大小,通常不超过100行代码。

## 示例

### 好的提示示例

1. "在 `utils/math_operations.py` 中添加一个函数 `calculate_average`,它接受一个数字列表作为输入并返回它们的平均值。"

2. "修复 `frontend/src/components/UserProfile.tsx` 中第42行发生的 TypeError。错误表明我们试图访问 undefined 的属性。"

3. "为注册表单中的电子邮件字段实现输入验证。更新 `frontend/src/components/RegistrationForm.tsx` 以在提交之前检查电子邮件是否为有效格式。"

### 不好的提示示例

1. "让代码更好。"(太模糊,不具体)

2. "重写整个后端以使用不同的框架。"(范围不合适)

3. "用户身份验证中有一个错误。你能找到并修复它吗?"(缺乏具体性和位置信息)

## 有效提示的技巧

1. 尽可能具体地说明期望的结果或要解决的问题。
2. 提供上下文,包括相关的文件路径和行号(如果可用)。
3. 将大型任务分解为更小、更易管理的提示。
4. 包括任何相关的错误消息或日志。
5. 如果从上下文中不明显,请指定编程语言或框架。

请记住,您的提示越精确和信息量大,AI 就越能帮助您开发或修改 OpenHands 软件。

有关更多有用提示的示例,请参阅 [OpenHands 入门](./getting-started)。
