# 运行 OpenHands

## 系统要求

- 支持 [Docker Desktop](https://docs.docker.com/desktop/setup/install/mac-install/#system-requirements) 的 MacOS
- Linux
- 配有 [WSL](https://learn.microsoft.com/en-us/windows/wsl/install) 和 [Docker Desktop 支持](https://docs.docker.com/desktop/setup/install/windows-install/#system-requirements) 的 Windows

建议使用具有现代处理器和至少 **4GB RAM** 的系统来运行 OpenHands。

## 前提条件

<details>
  <summary>MacOS</summary>

  **Docker Desktop**

  1. [在 Mac 上安装 Docker Desktop](https://docs.docker.com/desktop/setup/install/mac-install)。
  2. 打开 Docker Desktop，进入 `Settings > Advanced` 并确保启用了 `Allow the default Docker socket to be used`。
</details>

<details>
  <summary>Linux</summary>

  :::note
  已在 Ubuntu 22.04 上测试通过。
  :::

  **Docker Desktop**

  1. [在 Linux 上安装 Docker Desktop](https://docs.docker.com/desktop/setup/install/linux/)。

</details>

<details>
  <summary>Windows</summary>

  **WSL**

  1. [安装 WSL](https://learn.microsoft.com/en-us/windows/wsl/install)。
  2. 在 powershell 中运行 `wsl --version` 并确认 `Default Version: 2`。

  **Docker Desktop**

  1. [在 Windows 上安装 Docker Desktop](https://docs.docker.com/desktop/setup/install/windows-install)。
  2. 打开 Docker Desktop，进入 `Settings` 并确认以下设置：
  - General: 启用 `Use the WSL 2 based engine`。
  - Resources > WSL Integration: 启用 `Enable integration with my default WSL distro`。

  :::note
  下面启动应用的 docker 命令必须在 WSL 终端内运行。
  :::

</details>

## 启动应用

运行 OpenHands 最简单的方法是使用 Docker。

```bash
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik

docker run -it --rm --pull=always \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik \
    -e LOG_ALL_EVENTS=true \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v ~/.openhands-state:/.openhands-state \
    -p 3000:3000 \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app \
    docker.all-hands.dev/all-hands-ai/openhands:0.39
```

OpenHands 将在 http://localhost:3000 运行！

你还可以[将 OpenHands 连接到本地文件系统](https://docs.all-hands.dev/modules/usage/runtimes/docker#connecting-to-your-filesystem)，
以[无头模式](https://docs.all-hands.dev/modules/usage/how-to/headless-mode)运行 OpenHands，
通过[友好的 CLI](https://docs.all-hands.dev/modules/usage/how-to/cli-mode)与其交互，
或者使用 [GitHub action](https://docs.all-hands.dev/modules/usage/how-to/github-action) 在标记的问题上运行它。

## 设置

启动 OpenHands 后，你**必须**选择一个 `LLM Provider` 和 `LLM Model` 并输入相应的 `API Key`。
这可以在初始设置弹窗中完成，或者通过在 UI 中选择 `Settings` 按钮（齿轮图标）来完成。

如果列表中不存在所需的模型，你可以切换 `Advanced` 选项，并在 `Custom Model` 文本框中手动输入带有正确前缀的模型。
`Advanced` 选项还允许你在需要时指定 `Base URL`。

### 获取 API 密钥

OpenHands 需要 API 密钥才能访问大多数语言模型。以下是从推荐提供商获取 API 密钥的方法：

#### Anthropic (Claude)

1. [创建 Anthropic 账户](https://console.anthropic.com/)。
2. [生成 API 密钥](https://console.anthropic.com/settings/keys)。
3. [设置计费](https://console.anthropic.com/settings/billing)。

考虑设置使用限制以控制成本。

#### OpenAI

1. [创建 OpenAI 账户](https://platform.openai.com/)。
2. [生成 API 密钥](https://platform.openai.com/api-keys)。
3. [设置计费](https://platform.openai.com/account/billing/overview)。

现在你已准备好[开始使用 OpenHands](./getting-started)。

## 版本

[上面的 docker 命令](./installation#start-the-app)会拉取 OpenHands 的最新稳定版本。你还有其他选择：
- 对于特定版本，请在 `openhands:$VERSION` 和 `runtime:$VERSION` 中将 $VERSION 替换为版本号。
我们使用 SemVer，因此 `0.9` 将自动指向最新的 `0.9.x` 版本，而 `0` 将指向最新的 `0.x.x` 版本。
- 对于最新的开发版本，请在 `openhands:$VERSION` 和 `runtime:$VERSION` 中将 $VERSION 替换为 `main`。
此版本不稳定，仅推荐用于测试或开发目的。

有关开发工作流程，请参阅 [Development.md](https://github.com/All-Hands-AI/OpenHands/blob/main/Development.md)。

遇到问题？请查看我们的[故障排除指南](https://docs.all-hands.dev/modules/usage/troubleshooting)。
