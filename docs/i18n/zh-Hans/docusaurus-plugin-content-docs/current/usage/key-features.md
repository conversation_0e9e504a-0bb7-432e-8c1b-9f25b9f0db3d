# OpenHands 功能概览

![概览](/img/oh-features.png)

### 聊天面板
- 显示用户与 OpenHands 之间的对话。
- OpenHands 在此面板中解释其操作。

### 变更
- 显示 OpenHands 执行的文件变更。

### VS Code
- 嵌入式 VS Code，用于浏览和修改文件。
- 也可用于上传和下载文件。

### 终端
- 供 OpenHands 和用户运行终端命令的空间。

### Jupyter
- 显示 OpenHands 执行的所有 Python 命令。
- 在使用 OpenHands 执行数据可视化任务时特别有用。

### 应用
- 当 OpenHands 运行应用程序时显示网络服务器。
- 用户可以与正在运行的应用程序交互。

### 浏览器
- 由 OpenHands 用于浏览网站。
- 浏览器是非交互式的。
