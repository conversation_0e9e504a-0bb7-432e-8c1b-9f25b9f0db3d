# 关于 OpenHands

## 研究策略

通过LLM实现对生产级应用程序的完全复制是一项复杂的工作。我们的策略包括：

- **核心技术研究：** 专注于基础研究，以理解并改进代码生成和处理的技术方面。
- **任务规划：** 开发用于错误检测、代码库管理和优化的能力。
- **评估：** 建立全面的评估指标，以更好地理解和改进我们的智能体。

## 默认智能体

我们当前的默认智能体是[CodeActAgent](agents)，它能够生成代码并处理文件。

## 技术栈

OpenHands使用多种强大的框架和库构建，为其开发提供了坚实的基础。以下是项目中使用的关键技术：

![FastAPI](https://img.shields.io/badge/FastAPI-black?style=for-the-badge) ![uvicorn](https://img.shields.io/badge/uvicorn-black?style=for-the-badge) ![LiteLLM](https://img.shields.io/badge/LiteLLM-black?style=for-the-badge) ![Docker](https://img.shields.io/badge/Docker-black?style=for-the-badge) ![Ruff](https://img.shields.io/badge/Ruff-black?style=for-the-badge) ![MyPy](https://img.shields.io/badge/MyPy-black?style=for-the-badge) ![LlamaIndex](https://img.shields.io/badge/LlamaIndex-black?style=for-the-badge) ![React](https://img.shields.io/badge/React-black?style=for-the-badge)

请注意，这些技术的选择仍在进行中，随着项目的发展，可能会添加新技术或移除现有技术。我们努力采用最合适和高效的工具来增强OpenHands的能力。

## 许可证

根据MIT [许可证](https://github.com/All-Hands-AI/OpenHands/blob/main/LICENSE)分发。
