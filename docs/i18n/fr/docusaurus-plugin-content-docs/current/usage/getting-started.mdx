# Premiers pas avec OpenHands

Vous avez [exécuté OpenHands](./installation) et vous avez
[configuré votre LLM](./installation#setup). Et maintenant ?

OpenHands peut vous aider pour diverses tâches d'ingénierie. Cependant, la technologie est encore nouvelle, et nous sommes loin d'avoir
des agents capables de gérer des tâches complexes de manière autonome. Il est important de comprendre ce que l'agent fait bien et où il
a besoin de soutien.

## Hello World

Commencez par un simple exemple "hello world". Cela pourrait être plus délicat qu'il n'y paraît !

Demandez à l'agent :
> Écrivez un script bash hello.sh qui affiche "hello world!"

L'agent écrira le script, définira les permissions correctes et l'exécutera pour vérifier la sortie.

Vous pouvez continuer à demander à l'agent d'affiner votre code. C'est une excellente façon de
travailler avec les agents. Commencez simplement, puis itérez.

> Modifiez hello.sh pour qu'il accepte un nom comme premier argument, mais utilise "world" par défaut

Vous pouvez également utiliser n'importe quel langage dont vous avez besoin. L'agent peut avoir besoin de temps pour configurer l'environnement.

> Veuillez convertir hello.sh en script Ruby, et exécutez-le

## Construire à partir de zéro

Les agents excellent dans les tâches "greenfield", où ils n'ont pas besoin de contexte sur le code existant et
peuvent partir de zéro.
Commencez par une tâche simple et itérez à partir de là. Soyez précis sur ce que vous voulez et la pile technologique.

Par exemple, nous pourrions construire une application TODO :

> Construisez une application TODO frontend uniquement en React. Tout l'état doit être stocké dans localStorage.

Une fois la structure de base en place, continuez à affiner :

> Permettez d'ajouter une date d'échéance optionnelle à chaque tâche.

Comme pour le développement normal, committez et poussez votre code souvent.
De cette façon, vous pouvez toujours revenir à un état antérieur si l'agent s'égare.
Vous pouvez demander à l'agent de committer et pousser pour vous :

> Committez les changements et poussez-les vers une nouvelle branche appelée "feature/due-dates"

## Ajouter du nouveau code

OpenHands est excellent pour ajouter du nouveau code à une base de code existante.

Par exemple, vous pouvez demander à OpenHands d'ajouter une action GitHub qui vérifie votre code. Il pourrait vérifier votre base de code pour
déterminer le langage, puis créer un nouveau fichier dans `./github/workflows/lint.yml`.

> Ajoutez une action GitHub qui vérifie le code dans ce dépôt.

Certaines tâches nécessitent plus de contexte. Bien qu'OpenHands puisse utiliser des commandes comme ls et grep pour rechercher, fournir du contexte dès le départ
accélère les choses et réduit l'utilisation de tokens.

> Modifiez ./backend/api/routes.js pour ajouter une nouvelle route qui renvoie une liste de toutes les tâches.

> Ajoutez un nouveau composant React au répertoire ./frontend/components pour afficher une liste de Widgets.
> Il devrait utiliser le composant Widget existant.

## Refactoring

OpenHands est très efficace pour refactoriser du code en petits morceaux. Plutôt que de réarchitecturer l'ensemble de la base de code,
il est plus efficace de décomposer les fichiers et fonctions longs ou de renommer des variables.

> Renommez toutes les variables à une seule lettre dans ./app.go.

> Divisez la fonction `build_and_deploy_widgets` en deux fonctions, `build_widgets` et `deploy_widgets` dans widget.php.

> Décomposez ./api/routes.js en fichiers séparés pour chaque route.

## Corrections de bugs

OpenHands peut aider à traquer et corriger des bugs, mais la correction de bugs peut être délicate et nécessite souvent plus de contexte.
C'est utile si vous avez déjà diagnostiqué le problème et avez juste besoin qu'OpenHands gère la logique.

> Le champ email dans le point de terminaison `/subscribe` rejette les domaines .io. Corrigez cela.

> La fonction `search_widgets` dans ./app.py effectue une recherche sensible à la casse. Rendez-la insensible à la casse.

Pour la correction de bugs, le développement piloté par les tests peut être vraiment utile. Vous pouvez demander à l'agent d'écrire un nouveau test et d'itérer
jusqu'à ce que le bug soit corrigé :

> La fonction `hello` plante sur une chaîne vide. Écrivez un test qui reproduit ce bug, puis corrigez le code pour qu'il passe.

## Plus

OpenHands peut vous aider pour presque n'importe quelle tâche de codage, mais il faut un peu de pratique pour obtenir les meilleurs résultats.
Gardez ces conseils à l'esprit :
* Gardez vos tâches petites.
* Soyez précis.
* Fournissez beaucoup de contexte.
* Committez et poussez fréquemment.

Consultez [Meilleures pratiques de prompt](./prompting/prompting-best-practices) pour plus de conseils sur la façon de tirer le meilleur parti d'OpenHands.
