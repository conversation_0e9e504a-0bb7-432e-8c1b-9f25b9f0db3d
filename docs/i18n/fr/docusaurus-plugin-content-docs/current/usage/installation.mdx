# Exécution d'OpenHands

## Configuration système requise

- MacOS avec [support Docker Desktop](https://docs.docker.com/desktop/setup/install/mac-install/#system-requirements)
- Linux
- Windows avec [WSL](https://learn.microsoft.com/en-us/windows/wsl/install) et [support Docker Desktop](https://docs.docker.com/desktop/setup/install/windows-install/#system-requirements)

Un système avec un processeur moderne et un minimum de **4 Go de RAM** est recommandé pour exécuter OpenHands.

## Prérequis

<details>
  <summary>MacOS</summary>

  **Docker Desktop**

  1. [Installer Docker Desktop sur Mac](https://docs.docker.com/desktop/setup/install/mac-install).
  2. Ouvrez Docker Desktop, allez dans `Settings > Advanced` et assurez-vous que `Allow the default Docker socket to be used` est activé.
</details>

<details>
  <summary>Linux</summary>

  :::note
  Testé avec Ubuntu 22.04.
  :::

  **Docker Desktop**

  1. [Installer Docker Desktop sur Linux](https://docs.docker.com/desktop/setup/install/linux/).

</details>

<details>
  <summary>Windows</summary>

  **WSL**

  1. [Installer WSL](https://learn.microsoft.com/en-us/windows/wsl/install).
  2. Exécutez `wsl --version` dans powershell et confirmez `Default Version: 2`.

  **Docker Desktop**

  1. [Installer Docker Desktop sur Windows](https://docs.docker.com/desktop/setup/install/windows-install).
  2. Ouvrez Docker Desktop, allez dans `Settings` et confirmez les points suivants :
  - General: `Use the WSL 2 based engine` est activé.
  - Resources > WSL Integration: `Enable integration with my default WSL distro` est activé.

  :::note
  La commande docker ci-dessous pour démarrer l'application doit être exécutée dans le terminal WSL.
  :::

</details>

## Démarrer l'application

La façon la plus simple d'exécuter OpenHands est dans Docker.

```bash
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik

docker run -it --rm --pull=always \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik \
    -e LOG_ALL_EVENTS=true \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v ~/.openhands-state:/.openhands-state \
    -p 3000:3000 \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app \
    docker.all-hands.dev/all-hands-ai/openhands:0.39
```

Vous trouverez OpenHands en cours d'exécution à l'adresse http://localhost:3000 !

Vous pouvez également [connecter OpenHands à votre système de fichiers local](https://docs.all-hands.dev/modules/usage/runtimes/docker#connecting-to-your-filesystem),
exécuter OpenHands en [mode headless](https://docs.all-hands.dev/modules/usage/how-to/headless-mode) scriptable,
interagir avec lui via une [CLI conviviale](https://docs.all-hands.dev/modules/usage/how-to/cli-mode),
ou l'exécuter sur des problèmes étiquetés avec [une action GitHub](https://docs.all-hands.dev/modules/usage/how-to/github-action).

## Configuration

Après avoir lancé OpenHands, vous **devez** sélectionner un `LLM Provider` et un `LLM Model` et saisir une `API Key` correspondante.
Cela peut être fait lors de la fenêtre contextuelle des paramètres initiaux ou en sélectionnant le bouton `Settings`
(icône d'engrenage) dans l'interface utilisateur.

Si le modèle requis n'existe pas dans la liste, vous pouvez activer les options `Advanced` et le saisir manuellement avec le préfixe correct
dans la zone de texte `Custom Model`.
Les options `Advanced` vous permettent également de spécifier une `Base URL` si nécessaire.

### Obtenir une clé API

OpenHands nécessite une clé API pour accéder à la plupart des modèles de langage. Voici comment obtenir une clé API auprès des fournisseurs recommandés :

#### Anthropic (Claude)

1. [Créez un compte Anthropic](https://console.anthropic.com/).
2. [Générez une clé API](https://console.anthropic.com/settings/keys).
3. [Configurez la facturation](https://console.anthropic.com/settings/billing).

Envisagez de définir des limites d'utilisation pour contrôler les coûts.

#### OpenAI

1. [Créez un compte OpenAI](https://platform.openai.com/).
2. [Générez une clé API](https://platform.openai.com/api-keys).
3. [Configurez la facturation](https://platform.openai.com/account/billing/overview).

Vous êtes maintenant prêt à [commencer avec OpenHands](./getting-started).

## Versions

La [commande docker ci-dessus](./installation#start-the-app) extrait la version stable la plus récente d'OpenHands. Vous avez également d'autres options :
- Pour une version spécifique, remplacez $VERSION dans `openhands:$VERSION` et `runtime:$VERSION` par le numéro de version.
Nous utilisons SemVer, donc `0.9` pointera automatiquement vers la dernière version `0.9.x`, et `0` pointera vers la dernière version `0.x.x`.
- Pour la version de développement la plus à jour, remplacez $VERSION dans `openhands:$VERSION` et `runtime:$VERSION` par `main`.
Cette version est instable et est recommandée uniquement à des fins de test ou de développement.

Pour le flux de travail de développement, consultez [Development.md](https://github.com/All-Hands-AI/OpenHands/blob/main/Development.md).

Vous rencontrez des problèmes ? Consultez notre [Guide de dépannage](https://docs.all-hands.dev/modules/usage/troubleshooting).
