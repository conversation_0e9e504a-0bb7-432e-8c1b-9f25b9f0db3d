# Interface Cloud

L'interface Cloud fournit une interface web pour interagir avec OpenHands AI. Cette page explique comment accéder et utiliser l'interface Cloud d'OpenHands.

## Accès à l'Interface

L'interface Cloud d'OpenHands est accessible à [app.all-hands.dev](https://app.all-hands.dev). Vous devrez vous connecter avec votre compte GitHub ou GitLab pour accéder à l'interface.

<!-- Image will be added in a future update -->
<!-- ![Interface Cloud OpenHands](/img/docs/openhands-cloud-ui.png) -->

## Fonctionnalités Clés

Pour des informations détaillées sur les fonctionnalités disponibles dans l'interface Cloud d'OpenHands, veuillez consulter la section [Fonctionnalités Clés](../key-features.md) de la documentation.

## Paramètres

La page des paramètres vous permet de :

1. Configurer les préférences de votre compte
2. Gérer l'accès aux dépôts
3. Générer des clés API pour un accès programmatique
4. Personnaliser votre expérience OpenHands

## Prochaines Étapes

- [Utiliser le Résolveur de Problèmes Cloud](./cloud-issue-resolver.md) pour automatiser les corrections de code et obtenir de l'aide
- [En savoir plus sur l'API Cloud](./cloud-api.md) pour un accès programmatique
- [Retourner à la Mise en Route](./openhands-cloud.md)
