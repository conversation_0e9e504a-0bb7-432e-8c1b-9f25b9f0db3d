# OpenHands Cloud

OpenHands Cloud est la version hébergée dans le cloud d'OpenHands par All Hands AI.

## Accès à OpenHands Cloud

Pour commencer avec OpenHands Cloud, visitez [app.all-hands.dev](https://app.all-hands.dev).

Vous serez invité à vous connecter avec votre compte GitHub ou GitLab :

1. Après avoir lu et accepté les conditions d'utilisation, cliquez sur `Se connecter à GitHub` ou `Se connecter à GitLab`.
2. Examinez les permissions demandées par OpenHands et autorisez l'application.
   - OpenHands nécessitera certaines permissions de votre compte. Pour en savoir plus sur ces permissions,
     vous pouvez cliquer sur le lien `En savoir plus` sur la page d'autorisation.

## Prochaines Étapes

Une fois que vous avez connecté votre compte, vous pouvez :

- [Installer l'Intégration GitHub](./github-installation.md) pour utiliser OpenHands avec vos dépôts GitHub
- [Installer l'Intégration GitLab](./gitlab-installation.md) pour utiliser OpenHands avec vos dépôts GitLab
- [Accéder à l'Interface Cloud](./cloud-ui.md) pour interagir avec l'interface web
- [Utiliser l'API Cloud](./cloud-api.md) pour interagir programmatiquement avec OpenHands
- [Configurer le Résolveur de Problèmes Cloud](./cloud-issue-resolver.md) pour automatiser les corrections de code et fournir une assistance intelligente
