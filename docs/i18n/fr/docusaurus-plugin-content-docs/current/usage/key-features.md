# Aperçu des Fonctionnalités d'OpenHands

![aperçu](/img/oh-features.png)

### Panneau de Discussion
- Affiche la conversation entre l'utilisateur et OpenHands.
- OpenHands explique ses actions dans ce panneau.

### Modifications
- Montre les modifications de fichiers effectuées par OpenHands.

### VS Code
- VS Code intégré pour parcourir et modifier les fichiers.
- Peut également être utilisé pour télécharger et envoyer des fichiers.

### Terminal
- Un espace permettant à OpenHands et aux utilisateurs d'exécuter des commandes terminal.

### Jupyter
- Affiche toutes les commandes Python exécutées par OpenHands.
- Particulièrement utile lors de l'utilisation d'OpenHands pour des tâches de visualisation de données.

### Application
- Affiche le serveur web lorsqu'OpenHands exécute une application.
- Les utilisateurs peuvent interagir avec l'application en cours d'exécution.

### Navigateur
- Utilisé par OpenHands pour naviguer sur les sites web.
- Le navigateur n'est pas interactif.
