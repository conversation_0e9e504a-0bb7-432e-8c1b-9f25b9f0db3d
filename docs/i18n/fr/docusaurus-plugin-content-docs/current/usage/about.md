# À propos d'OpenHands

## Stratégie de recherche

Réaliser une réplication complète d'applications de qualité production avec des LLM est une entreprise complexe. Notre stratégie comprend :

- **Recherche technique fondamentale :** Concentration sur la recherche fondamentale pour comprendre et améliorer les aspects techniques de la génération et de la gestion du code.
- **Planification des tâches :** Développement de capacités pour la détection de bugs, la gestion de base de code et l'optimisation.
- **Évaluation :** Établissement de métriques d'évaluation complètes pour mieux comprendre et améliorer nos agents.

## Agent par défaut

Notre Agent par défaut est actuellement le [CodeActAgent](agents), qui est capable de générer du code et de gérer des fichiers.

## Construit avec

OpenHands est construit en utilisant une combinaison de frameworks et bibliothèques puissants, fournissant une base solide pour son développement. Voici les technologies clés utilisées dans le projet :

![FastAPI](https://img.shields.io/badge/FastAPI-black?style=for-the-badge) ![uvicorn](https://img.shields.io/badge/uvicorn-black?style=for-the-badge) ![LiteLLM](https://img.shields.io/badge/LiteLLM-black?style=for-the-badge) ![Docker](https://img.shields.io/badge/Docker-black?style=for-the-badge) ![Ruff](https://img.shields.io/badge/Ruff-black?style=for-the-badge) ![MyPy](https://img.shields.io/badge/MyPy-black?style=for-the-badge) ![LlamaIndex](https://img.shields.io/badge/LlamaIndex-black?style=for-the-badge) ![React](https://img.shields.io/badge/React-black?style=for-the-badge)

Veuillez noter que la sélection de ces technologies est en cours, et des technologies supplémentaires peuvent être ajoutées ou des existantes peuvent être supprimées à mesure que le projet évolue. Nous nous efforçons d'adopter les outils les plus appropriés et efficaces pour améliorer les capacités d'OpenHands.

## Licence

Distribué sous la [Licence](https://github.com/All-Hands-AI/OpenHands/blob/main/LICENSE) MIT.
