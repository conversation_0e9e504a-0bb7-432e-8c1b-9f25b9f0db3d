# ランタイム設定

:::note
このセクションは、OpenHandsでDockerとは異なるランタイムを使用したいユーザー向けです。
:::

ランタイムとは、OpenHandsエージェントがファイルを編集しコマンドを実行できる環境のことです。

デフォルトでは、OpenHandsはローカルコンピュータ上で動作する[Dockerベースのランタイム](./runtimes/docker)を使用します。
これにより、使用するLLMの費用のみを支払えば良く、コードはLLMにのみ送信されます。

また、通常はサードパーティによって管理される他のランタイムもサポートしています。

さらに、Dockerを使わずにマシン上で直接実行する[ローカルランタイム](./runtimes/local)も提供しており、
CIパイプラインのような制御された環境で役立ちます。

## 利用可能なランタイム

OpenHandsはいくつかの異なるランタイム環境をサポートしています：

- [Dockerランタイム](./runtimes/docker.md) - 分離のためにDockerコンテナを使用するデフォルトのランタイム（ほとんどのユーザーに推奨）。
- [OpenHandsリモートランタイム](./runtimes/remote.md) - 並列実行のためのクラウドベースのランタイム（ベータ版）。
- [Modalランタイム](./runtimes/modal.md) - パートナーであるModalが提供するランタイム。
- [Daytonaランタイム](./runtimes/daytona.md) - Daytonaが提供するランタイム。
- [ローカルランタイム](./runtimes/local.md) - Dockerを使わずにローカルマシン上で直接実行。
