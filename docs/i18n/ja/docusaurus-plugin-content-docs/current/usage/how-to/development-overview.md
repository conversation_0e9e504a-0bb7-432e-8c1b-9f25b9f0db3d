---
sidebar_position: 9
---

# 開発概要

このガイドでは、OpenHandsリポジトリで利用可能な主要なドキュメントリソースの概要を提供します。貢献したい、アーキテクチャを理解したい、または特定のコンポーネントに取り組みたいと考えている場合でも、これらのリソースはコードベースを効果的に操作するのに役立ちます。

## コアドキュメント

### プロジェクトの基本
- **メインプロジェクト概要** (`/README.md`)
  OpenHandsを理解するための主要なエントリーポイントで、機能と基本的なセットアップ手順が含まれています。

- **開発ガイド** (`/Development.md`)
  OpenHandsに取り組む開発者向けの包括的なガイドで、セットアップ、要件、開発ワークフローが含まれています。

- **貢献ガイドライン** (`/CONTRIBUTING.md`)
  貢献者向けの重要な情報で、コードスタイル、PRプロセス、貢献ワークフローをカバーしています。

### コンポーネントドキュメント

#### フロントエンド
- **フロントエンドアプリケーション** (`/frontend/README.md`)
  Reactベースのフロントエンドアプリケーションのセットアップと開発のための完全なガイド。

#### バックエンド
- **バックエンド実装** (`/openhands/README.md`)
  Pythonバックエンドの実装とアーキテクチャに関する詳細なドキュメント。

- **サーバードキュメント** (`/openhands/server/README.md`)
  サーバー実装の詳細、APIドキュメント、サービスアーキテクチャ。

- **ランタイム環境** (`/openhands/runtime/README.md`)
  ランタイム環境、実行モデル、ランタイム構成をカバーするドキュメント。

#### インフラストラクチャ
- **コンテナドキュメント** (`/containers/README.md`)
  Dockerコンテナ、デプロイメント戦略、コンテナ管理に関する包括的な情報。

### テストと評価
- **ユニットテストガイド** (`/tests/unit/README.md`)
  ユニットテストの作成、実行、保守に関する指示。

- **評価フレームワーク** (`/evaluation/README.md`)
  評価フレームワーク、ベンチマーク、パフォーマンステストに関するドキュメント。

### 高度な機能
- **マイクロエージェントアーキテクチャ** (`/microagents/README.md`)
  マイクロエージェントアーキテクチャ、実装、使用法に関する詳細情報。

### ドキュメント標準
- **ドキュメントスタイルガイド** (`/docs/DOC_STYLE_GUIDE.md`)
  プロジェクトドキュメントの作成と保守のための標準とガイドライン。

## 開発を始める

OpenHandsでの開発が初めての場合は、次の順序に従うことをお勧めします：

1. プロジェクトの目的と機能を理解するために、メインの`README.md`から始める
2. 貢献する予定がある場合は、`CONTRIBUTING.md`のガイドラインを確認する
3. `Development.md`のセットアップ手順に従う
4. 興味のある分野に基づいて特定のコンポーネントドキュメントに深く入り込む：
   - フロントエンド開発者は`/frontend/README.md`に焦点を当てるべき
   - バックエンド開発者は`/openhands/README.md`から始めるべき
   - インフラストラクチャ作業は`/containers/README.md`から始めるべき

## ドキュメントの更新

コードベースに変更を加える際は、以下を確認してください：
1. 関連するドキュメントが変更を反映するように更新されている
2. 新機能が適切なREADMEファイルに文書化されている
3. APIの変更がサーバードキュメントに反映されている
4. ドキュメントが`/docs/DOC_STYLE_GUIDE.md`のスタイルガイドに従っている
