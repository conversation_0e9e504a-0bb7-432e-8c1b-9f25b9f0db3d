# GUIモード

OpenHandsは、AIアシスタントとの対話のためのグラフィカルユーザーインターフェース（GUI）モードを提供しています。

## インストールとセットアップ

1. インストール手順に従ってOpenHandsをインストールします。
2. コマンドを実行した後、[http://localhost:3000](http://localhost:3000)でOpenHandsにアクセスします。

## GUIでの対話

### 初期設定

1. 初回起動時に設定ポップアップが表示されます。
2. ドロップダウンメニューから`LLMプロバイダー`と`LLMモデル`を選択します。必要なモデルがリストに存在しない場合は、
   `詳細設定を表示`を選択してください。次に`詳細設定`をトグルして、
   `カスタムモデル`テキストボックスに正しいプレフィックスを付けて入力します。
3. 選択したプロバイダーに対応する`APIキー`を入力します。
4. `変更を保存`をクリックして設定を適用します。

### バージョン管理トークン

OpenHandsは複数のバージョン管理プロバイダーをサポートしています。複数のプロバイダーのトークンを同時に設定できます。

#### GitHubトークンの設定

OpenHandsは提供された場合、自動的に`GITHUB_TOKEN`をシェル環境にエクスポートします：

<details>
  <summary>GitHubトークンの設定方法</summary>

  1. **個人アクセストークン（PAT）の生成**：
   - GitHubで、設定 > 開発者設定 > 個人アクセストークン > トークン（クラシック）に移動します。
   - **新しいトークン（クラシック）**
     - 必要なスコープ：
     - `repo`（プライベートリポジトリの完全な制御）
   - **細かい権限を持つトークン**
     - すべてのリポジトリ（特定のリポジトリを選択することもできますが、リポジトリ検索の結果に影響します）
     - 最小限の権限（検索用に`メタデータ = 読み取り専用`、ブランチ作成用に`プルリクエスト = 読み取りと書き込み`および`コンテンツ = 読み取りと書き込み`を選択）
  2. **OpenHandsにトークンを入力**：
   - 設定ボタン（歯車アイコン）をクリックします。
   - `GitHubトークン`フィールドにトークンを貼り付けます。
   - `保存`をクリックして変更を適用します。
</details>

<details>
  <summary>組織のトークンポリシー</summary>

  組織のリポジトリを扱う場合、追加の設定が必要な場合があります：

  1. **組織の要件を確認**：
   - 組織の管理者は特定のトークンポリシーを強制する場合があります。
   - 一部の組織では、SSOが有効になっているトークンの作成が必要です。
   - 組織の[トークンポリシー設定](https://docs.github.com/en/organizations/managing-programmatic-access-to-your-organization/setting-a-personal-access-token-policy-for-your-organization)を確認してください。
  2. **組織へのアクセスを確認**：
   - GitHubのトークン設定に移動します。
   - `組織アクセス`の下で組織を探します。
   - 必要に応じて、組織の横にある`SSOを有効にする`をクリックします。
   - SSO認証プロセスを完了します。
</details>

<details>
  <summary>トラブルシューティング</summary>

  一般的な問題と解決策：

  - **トークンが認識されない**：
     - トークンが設定に正しく保存されていることを確認します。
     - トークンが期限切れになっていないか確認します。
     - トークンに必要なスコープがあることを確認します。
     - トークンを再生成してみてください。

  - **組織アクセスが拒否された**：
     - SSOが必要だが有効になっていないか確認します。
     - 組織のメンバーシップを確認します。
     - トークンポリシーがアクセスをブロックしている場合は、組織の管理者に連絡してください。

  - **トークンが機能するか確認**：
     - トークンが有効な場合、アプリは緑色のチェックマークを表示します。
     - リポジトリにアクセスして権限を確認してみてください。
     - ブラウザのコンソールでエラーメッセージを確認してください。
</details>

#### GitLabトークンの設定

OpenHandsは提供された場合、自動的に`GITLAB_TOKEN`をシェル環境にエクスポートします：

<details>
  <summary>GitLabトークンの設定方法</summary>

  1. **個人アクセストークン（PAT）の生成**：
   - GitLabで、ユーザー設定 > アクセストークンに移動します。
   - 以下のスコープを持つ新しいトークンを作成します：
     - `api`（APIアクセス）
     - `read_user`（ユーザー情報の読み取り）
     - `read_repository`（リポジトリの読み取り）
     - `write_repository`（リポジトリの書き込み）
   - 有効期限を設定するか、期限なしトークンの場合は空白のままにします。
  2. **OpenHandsにトークンを入力**：
   - 設定ボタン（歯車アイコン）をクリックします。
   - `GitLabトークン`フィールドにトークンを貼り付けます。
   - セルフホスト型GitLabを使用している場合は、GitLabインスタンスのURLを入力します。
   - `保存`をクリックして変更を適用します。
</details>

<details>
  <summary>トラブルシューティング</summary>

  一般的な問題と解決策：

  - **トークンが認識されない**：
     - トークンが設定に正しく保存されていることを確認します。
     - トークンが期限切れになっていないか確認します。
     - トークンに必要なスコープがあることを確認します。
     - セルフホスト型インスタンスの場合、正しいインスタンスURLを確認します。

  - **アクセスが拒否された**：
     - プロジェクトのアクセス権限を確認します。
     - トークンに必要なスコープがあるか確認します。
     - グループ/組織のリポジトリの場合、適切なアクセス権があることを確認します。
</details>

### 詳細設定

1. 設定ページ内で、`詳細設定`をトグルして追加設定にアクセスします。
2. リストにないモデルを手動で入力するには、`カスタムモデル`テキストボックスを使用します。
3. LLMプロバイダーが必要とする場合は、`ベースURL`を指定します。

### AIとの対話

1. 入力ボックスにプロンプトを入力します。
2. 送信ボタンをクリックするか、Enterキーを押してメッセージを送信します。
3. AIはあなたの入力を処理し、チャットウィンドウに応答を提供します。
4. フォローアップの質問や追加情報を提供することで会話を続けることができます。

## 効果的な使用のためのヒント

- [プロンプティングのベストプラクティス](../prompting/prompting-best-practices)で説明されているように、最も正確で役立つ回答を得るために、リクエストを具体的にしてください。
- [LLMセクション](usage/llms/llms.md)で説明されているように、推奨モデルのいずれかを使用してください。

OpenHandsのGUIモードは、AIアシスタントとの対話をできるだけスムーズで直感的にするように設計されています。生産性を最大化するために、その機能を遠慮なく探索してください。
