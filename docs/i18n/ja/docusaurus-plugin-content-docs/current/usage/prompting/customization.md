# リポジトリ固有の動作のカスタマイズ

OpenHandsは、リポジトリ固有のコンテキストとガイダンスを提供することで、特定のリポジトリでより効果的に機能するようにカスタマイズできます。このセクションでは、OpenHandsをプロジェクトに最適化する方法について説明します。

## リポジトリの設定

リポジトリのルートに`.openhands`ディレクトリを作成することで、OpenHandsの動作をリポジトリ用にカスタマイズできます。最低限、このディレクトリには`.openhands/microagents/repo.md`ファイルが含まれている必要があります。このファイルには、エージェントがこのリポジトリで作業するたびに与えられる指示が含まれています。

以下の情報を含めることをお勧めします。
1. **リポジトリの概要**: プロジェクトの目的とアーキテクチャの簡単な説明
2. **ディレクトリ構造**: 主要なディレクトリとその目的
3. **開発ガイドライン**: プロジェクト固有のコーディング標準と慣行
4. **テスト要件**: テストの実行方法と必要なテストの種類
5. **セットアップ手順**: プロジェクトのビルドと実行に必要な手順

### リポジトリ設定の例
`.openhands/microagents/repo.md`ファイルの例:
```
Repository: MyProject
Description: タスク管理用のWebアプリケーション

ディレクトリ構造:
- src/: アプリケーションのメインコード
- tests/: テストファイル
- docs/: ドキュメンテーション

セットアップ:
- `npm install`を実行して依存関係をインストールします
- 開発には`npm run dev`を使用します
- テストには`npm test`を実行します

ガイドライン:
- ESLint設定に従ってください
- 新機能にはすべてテストを書いてください
- 新しいコードにはTypeScriptを使用してください
```

### プロンプトのカスタマイズ

カスタマイズされたリポジトリで作業する場合:

1. **プロジェクトの標準を参照する**: プロジェクトで使用されている特定のコーディング標準やパターンに言及する
2. **コンテキストを含める**: 関連するドキュメンテーションや既存の実装を参照する
3. **テスト要件を指定する**: プロジェクト固有のテスト要件をプロンプトに含める

カスタムプロンプトの例:
```
既存のコンポーネントパターンに従って、src/components/TaskList.tsxにタスク完了の新機能を追加してください。
tests/components/にユニットテストを含め、docs/features/のドキュメントを更新してください。
コンポーネントはsrc/styles/componentsの共有スタイルを使用する必要があります。
```

### リポジトリカスタマイズのベストプラクティス

1. **指示を最新に保つ**: プロジェクトの進化に合わせて、`.openhands`ディレクトリを定期的に更新する
2. **具体的にする**: プロジェクト固有のパス、パターン、要件を含める
3. **依存関係を文書化する**: 開発に必要なすべてのツールと依存関係を列挙する
4. **例を含める**: プロジェクトからの良いコード例を提供する
5. **規約を指定する**: 命名規則、ファイル構成、コードスタイルの好みを文書化する

OpenHandsをリポジトリ用にカスタマイズすることで、プロジェクトの標準と要件に沿ったより正確で一貫した結果が得られます。

## その他のマイクロエージェント
`.openhands/microagents/`ディレクトリに、`test`、`frontend`、`migration`などの特定のキーワードが見つかった場合にエージェントに送信される追加の指示を作成できます。詳細については、[Microagents](microagents.md)を参照してください。
