# プロンプトのベストプラクティス

OpenHands AIソフトウェア開発者と連携する際、明確で効果的なプロンプトを提供することが、正確で有用な回答を得るための鍵となります。このガイドでは、効果的なプロンプトを作成するためのベストプラクティスを概説します。

## 良いプロンプトの特徴

良いプロンプトは以下の特徴を持ちます：

- **具体的**：追加すべき機能や修正すべきエラーを明確に説明する。
- **場所を特定**：可能であれば、修正すべきコードベース内の場所を指定する。
- **適切な範囲**：単一の機能に焦点を当て、通常は100行以内のコードに収める。

## 例

### 良いプロンプトの例

- `utils/math_operations.py`に、数値のリストを入力として受け取り、その平均を返す`calculate_average`関数を追加してください。
- `frontend/src/components/UserProfile.tsx`の42行目で発生しているTypeErrorを修正してください。エラーは、undefinedのプロパティにアクセスしようとしていることを示唆しています。
- 登録フォームのメールフィールドに入力検証を実装してください。`frontend/src/components/RegistrationForm.tsx`を更新して、送信前にメールが有効な形式かどうかを確認するようにしてください。

### 悪いプロンプトの例

- コードを改善してください。（あまりにも曖昧で、具体性がない）
- バックエンド全体を別のフレームワークを使用して書き直してください。（範囲が適切でない）
- ユーザー認証のどこかにバグがあります。見つけて修正できますか？（具体性と場所の情報が不足）

## 効果的なプロンプトのためのヒント

- 望ましい結果や解決すべき問題について、できるだけ具体的に説明する。
- 関連するファイルパスや行番号など、可能であればコンテキストを提供する。
- 大きなタスクを小さく管理しやすいプロンプトに分割する。
- 関連するエラーメッセージやログを含める。
- プログラミング言語やフレームワークが明らかでない場合は、それを指定する。

プロンプトが正確で情報量が多いほど、OpenHandsはより良くサポートできます。

役立つプロンプトの例については、[OpenHandsの使い方](../getting-started)をご覧ください。
