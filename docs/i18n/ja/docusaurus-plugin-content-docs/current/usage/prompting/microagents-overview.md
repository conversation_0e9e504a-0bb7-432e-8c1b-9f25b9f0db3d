# マイクロエージェント概要

マイクロエージェントは、OpenHandsにドメイン固有の知識を強化する特殊なプロンプトです。
専門的なガイダンスを提供し、一般的なタスクを自動化し、プロジェクト全体で一貫した実践を確保します。

## マイクロエージェントの種類

現在、OpenHandsは以下の種類のマイクロエージェントをサポートしています：

- [一般リポジトリマイクロエージェント](./microagents-repo)：リポジトリに関するOpenHandsの一般的なガイドライン。
- [キーワードトリガーマイクロエージェント](./microagents-keyword)：プロンプト内の特定のキーワードによって起動されるガイドライン。

OpenHandsの動作をカスタマイズするには、リポジトリのルートに`.openhands/microagents/`ディレクトリを作成し、
その中に`<microagent_name>.md`ファイルを追加します。

:::note
ロードされたマイクロエージェントはコンテキストウィンドウ内のスペースを占めます。
これらのマイクロエージェントは、ユーザーメッセージと共に、タスクと環境についてOpenHandsに情報を提供します。
:::

リポジトリ構造の例：

```
some-repository/
└── .openhands/
    └── microagents/
        └── repo.md            # 一般的なリポジトリガイドライン
        └── trigger_this.md    # 特定のキーワードでトリガーされるマイクロエージェント
        └── trigger_that.md    # 特定のキーワードでトリガーされるマイクロエージェント
```

## マイクロエージェントのフロントマター要件

各マイクロエージェントファイルには、追加情報を提供するフロントマターが含まれる場合があります。場合によっては、このフロントマターが必要です：

| マイクロエージェントの種類 | 必須 |
|--------------------------|------|
| `一般リポジトリマイクロエージェント` | いいえ |
| `キーワードトリガーマイクロエージェント` | はい |
