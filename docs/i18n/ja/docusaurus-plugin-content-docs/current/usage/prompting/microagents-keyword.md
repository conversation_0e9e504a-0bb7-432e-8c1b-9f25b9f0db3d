# キーワードトリガー型マイクロエージェント

## 目的

キーワードトリガー型マイクロエージェントは、プロンプト内に特定のキーワードが現れたときに有効化される特定の指示をOpenHandsに提供します。これは特定のツール、言語、またはフレームワークに基づいて動作をカスタマイズするのに役立ちます。

## 使用方法

これらのマイクロエージェントは、プロンプトにトリガーワードの1つが含まれている場合にのみ読み込まれます。

## フロントマターの構文

フロントマターはキーワードトリガー型マイクロエージェントには必須です。ファイルの先頭、ガイドラインの上に配置する必要があります。

フロントマターを三重のダッシュ(---)で囲み、以下のフィールドを含めます：

| フィールド   | 説明                                           | 必須     | デフォルト        |
|------------|------------------------------------------------|----------|------------------|
| `triggers` | マイクロエージェントを有効化するキーワードのリスト | はい     | なし              |
| `agent`    | このマイクロエージェントが適用されるエージェント   | いいえ    | 'CodeActAgent'   |


## 例

`.openhands/microagents/yummy.md`に配置されたキーワードトリガー型マイクロエージェントファイルの例：
```
---
triggers:
- yummyhappy
- happyyummy
---

ユーザーが魔法の言葉を言いました。「それはおいしかった！」と応答してください。
```

[公式OpenHandsリポジトリでキーワードによってトリガーされるマイクロエージェントの例を見る](https://github.com/All-Hands-AI/OpenHands/tree/main/microagents)
