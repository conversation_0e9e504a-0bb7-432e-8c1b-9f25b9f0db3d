# グローバルマイクロエージェント

## 概要

グローバルマイクロエージェントは、すべてのOpenHandsユーザーに適用される[キーワード起動型マイクロエージェント](./microagents-keyword)です。現在のグローバルマイクロエージェントのリストは[OpenHandsリポジトリ](https://github.com/All-Hands-AI/OpenHands/tree/main/microagents)で確認できます。

## グローバルマイクロエージェントの貢献

公式リポジトリにプルリクエストを開くことで、グローバルマイクロエージェントを作成してコミュニティと共有することができます。

OpenHandsへの貢献方法の具体的な手順については、[CONTRIBUTING.md](https://github.com/All-Hands-AI/OpenHands/blob/main/CONTRIBUTING.md)を参照してください。

### グローバルマイクロエージェントのベストプラクティス

- **明確な範囲**: マイクロエージェントを特定のドメインやタスクに焦点を当てる。
- **明示的な指示**: 明確で曖昧さのないガイドラインを提供する。
- **有用な例**: 一般的なユースケースの実用的な例を含める。
- **安全性優先**: 必要な警告と制約を含める。
- **統合の認識**: マイクロエージェントが他のコンポーネントとどのように相互作用するかを考慮する。

### グローバルマイクロエージェントを貢献するステップ

#### 1. グローバルマイクロエージェントの計画

グローバルマイクロエージェントを作成する前に、以下を考慮してください：

- どのような特定の問題やユースケースに対応するか？
- どのようなユニークな能力や知識を持つべきか？
- どのようなトリガーワードが活性化に適しているか？
- どのような制約やガイドラインに従うべきか？

#### 2. ファイルの作成

適切なディレクトリに説明的な名前を持つ新しいMarkdownファイルを作成します：
[`microagents/`](https://github.com/All-Hands-AI/OpenHands/tree/main/microagents)

#### 3. グローバルマイクロエージェントのテスト

- さまざまなプロンプトでエージェントをテストする。
- トリガーワードが正しくエージェントを活性化することを確認する。
- 指示が明確で包括的であることを確認する。
- 既存のエージェントとの潜在的な競合や重複をチェックする。

#### 4. 提出プロセス

以下を含むプルリクエストを提出します：

- 新しいマイクロエージェントファイル。
- 必要に応じて更新されたドキュメント。
- エージェントの目的と機能の説明。
