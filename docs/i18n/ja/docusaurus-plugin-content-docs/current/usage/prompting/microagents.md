# マイクロエージェント

OpenHandsは、特定のタスクやコンテキストを効率的に処理するために、特殊なマイクロエージェントを利用しています。これらのマイクロエージェントは、特定のシナリオに特化した動作と知識を提供する小さな目的指向のコンポーネントです。

## 概要

マイクロエージェントは、`openhands/agenthub/codeact_agent/micro/`ディレクトリ下のマークダウンファイルで定義されます。各マイクロエージェントは以下の設定を持ちます：

- 一意の名前
- エージェントのタイプ（通常はCodeActAgent）
- エージェントを起動するトリガーキーワード
- 特定の指示と能力

## 利用可能なマイクロエージェント

### GitHubエージェント
**ファイル**: `github.md`
**トリガー**: `github`, `git`

GitHubエージェントは、GitHub APIとのインタラクションとリポジトリ管理に特化しています。これは：
- API認証用の`GITHUB_TOKEN`にアクセスできる
- リポジトリとのインタラクションに厳密なガイドラインに従う
- ブランチとプルリクエストを管理する
- Webブラウザでのインタラクションの代わりにGitHub APIを使用する

主な機能：
- ブランチ保護（main/masterへの直接プッシュを防止）
- 自動PRの作成
- Git設定の管理
- GitHub操作のためのAPI中心のアプローチ

### NPMエージェント
**ファイル**: `npm.md`
**トリガー**: `npm`

npmパッケージの管理に特化し、特に以下に焦点を当てています：
- 非対話的なシェル操作
- Unixの'yes'コマンドを使用した自動確認の管理
- パッケージインストールの自動化

### カスタムマイクロエージェント

マイクロエージェントディレクトリに新しいマークダウンファイルを追加することで、独自のマイクロエージェントを作成できます。各ファイルは次の構造に従う必要があります：

```markdown
---
name: エージェント名
agent: CodeActAgent
triggers:
- トリガー1
- トリガー2
---

マイクロエージェントの指示と能力...
```

## ベストプラクティス

マイクロエージェントを使用する際は：

1. **適切なトリガーを使用する**: コマンドに関連するトリガーキーワードを含めて、正しいマイクロエージェントを起動するようにします
2. **エージェントのガイドラインに従う**: 各エージェントには特定の指示と制限があります - 最適な結果を得るためにそれらに従ってください
3. **API中心のアプローチ**: 可能な場合は、Webインターフェースではなく、APIエンドポイントを使用します
4. **自動化に優しい**: 非対話的な環境でうまく機能するコマンドを設計します

## 統合

マイクロエージェントは自動的にOpenHandsのワークフローに統合されます。これらは：
- 着信コマンドをモニタリングしてトリガーキーワードを検出する
- 関連するトリガーが検出されたときに起動する
- 特殊な知識と能力を適用する
- 特定のガイドラインと制限に従う

## 使用例

```bash
# GitHubエージェントの例
git checkout -b feature-branch
git commit -m "Add new feature"
git push origin feature-branch

# NPMエージェントの例
yes | npm install package-name
```

特定のエージェントの詳細については、マイクロエージェントディレクトリ内の個別のドキュメントファイルを参照してください。

## マイクロエージェントの貢献

OpenHandsに新しいマイクロエージェントを貢献するには、次のガイドラインに従ってください：

### 1. マイクロエージェントの計画

マイクロエージェントを作成する前に、以下を検討してください：
- どの特定の問題やユースケースに対処するのか？
- どのようなユニークな能力や知識を持つべきか？
- どのトリガーキーワードを使用するのが適切か？
- どのような制約やガイドラインに従うべきか？

### 2. ファイル構造

`openhands/agenthub/codeact_agent/micro/`に新しいマークダウンファイルを作成し、説明的な名前を付けます（例：Dockerに特化したエージェントの場合は`docker.md`）。

### 3. 必要なコンポーネント

マイクロエージェントファイルには以下を含める必要があります：

1. **フロントマター**: ファイルの先頭にYAMLメタデータ：
```markdown
---
name: エージェント名
agent: CodeActAgent
triggers:
- トリガー1
- トリガー2
---
```

2. **指示**: エージェントの動作に関する明確で具体的な指示：
```markdown
あなたは[特定のタスク/ドメイン]を担当しています。

主な責任：
1. [責任1]
2. [責任2]

ガイドライン：
- [ガイドライン1]
- [ガイドライン2]

使用例：
[例1]
[例2]
```

### 4. マイクロエージェント開発のベストプラクティス

1. **明確な範囲**: エージェントを特定のドメインまたはタスクに集中させる
2. **明示的な指示**: 明確で曖昧さのない指示を提供する
3. **有用な例**: 一般的なユースケースの実用的な例を含める
4. **セキュリティ第一**: 必要な警告と制約を含める
5. **統合の認識**: エージェントが他のコンポーネントとどのように相互作用するかを考慮する

### 5. マイクロエージェントのテスト

提出前に：
1. さまざまなプロンプトでエージェントをテストする
2. トリガーキーワードがエージェントを正しく起動することを確認する
3. 指示が明確で完全であることを確認する
4. 既存のエージェントとの潜在的な競合を確認する

### 6. 実装例

新しいマイクロエージェントのテンプレートを以下に示します：

```markdown
---
name: docker
agent: CodeActAgent
triggers:
- docker
- コンテナ
---

あなたはDockerコンテナの管理とDockerfileの作成を担当しています。

主な責任：
1. Dockerfileの作成と変更
2. コンテナのライフサイクル管理
3. Docker Compose設定の管理

ガイドライン：
- 可能な限り公式のベースイメージを使用する
- 必要なセキュリティの考慮事項を含める
- レイヤー最適化のためのDockerベストプラクティスに従う

例：
1. Dockerfileの作成：
   ```dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm install
   COPY . .
   CMD ["npm", "start"]
   ```

2. Docker Composeの使用：
   ```yaml
   version: '3'
   services:
     web:
       build: .
       ports:
         - "3000:3000"
   ```

忘れずに：
- Dockerfileの構文を検証する
- セキュリティの脆弱性を確認する
- ビルド時間とイメージサイズを最適化する
```

### 7. 提出プロセス

1. 適切なディレクトリにマイクロエージェントファイルを作成する
2. 徹底的にテストする
3. 以下を含むプルリクエストを提出する：
   - 新しいマイクロエージェントファイル
   - 必要に応じて更新されたドキュメント
   - エージェントの目的と能力の説明

マイクロエージェントは、特定のドメインでOpenHandsの能力を拡張する強力な方法であることを忘れないでください。適切に設計されたエージェントは、特殊なタスクを処理するシステムの能力を大幅に向上させることができます。
