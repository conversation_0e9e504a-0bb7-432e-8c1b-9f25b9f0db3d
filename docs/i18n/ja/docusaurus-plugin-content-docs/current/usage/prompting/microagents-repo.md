# 一般リポジトリマイクロエージェント

## 目的

リポジトリをより効果的に扱うためのOpenHandsの一般的なガイドライン。

## 使用方法

これらのマイクロエージェントは常にコンテキストの一部として読み込まれます。

## フロントマター構文

このタイプのマイクロエージェントのフロントマターはオプションです。

フロントマターは三重のダッシュ(---)で囲み、以下のフィールドを含めることができます：

| フィールド | 説明                                   | 必須     | デフォルト値    |
|-----------|-----------------------------------------|----------|----------------|
| `agent`   | このマイクロエージェントが適用されるエージェント | いいえ   | 'CodeActAgent' |

## 例

`.openhands/microagents/repo.md`に配置された一般リポジトリマイクロエージェントファイルの例：
```
このプロジェクトはユーザーがTODOアイテムを追跡できるTODOアプリケーションです。

セットアップするには、`npm run build`を実行できます。
変更をコミットする前に、常にテストが合格していることを確認してください。`npm run test`を実行してテストを実行できます。
```

[一般リポジトリマイクロエージェントの詳細な例はこちらをご覧ください。](https://github.com/All-Hands-AI/OpenHands/tree/main/.openhands/microagents)
