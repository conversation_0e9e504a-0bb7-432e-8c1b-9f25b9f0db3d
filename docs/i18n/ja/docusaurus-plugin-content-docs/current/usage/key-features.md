# OpenHands 機能概要

![概要](/img/oh-features.png)

### チャットパネル
- ユーザーとOpenHands間の会話を表示します。
- OpenHandsはこのパネルで自身の行動を説明します。

### 変更
- OpenHandsによって実行されたファイル変更を表示します。

### VS Code
- ファイルの閲覧や修正のための組み込みVS Code。
- ファイルのアップロードやダウンロードにも使用できます。

### ターミナル
- OpenHandsとユーザーがターミナルコマンドを実行するためのスペース。

### Jupyter
- OpenHandsによって実行されたすべてのPythonコマンドを表示します。
- データ可視化タスクにOpenHandsを使用する際に特に便利です。

### アプリ
- OpenHandsがアプリケーションを実行する際にウェブサーバーを表示します。
- ユーザーは実行中のアプリケーションと対話できます。

### ブラウザ
- OpenHandsがウェブサイトを閲覧するために使用します。
- ブラウザは非対話式です。
