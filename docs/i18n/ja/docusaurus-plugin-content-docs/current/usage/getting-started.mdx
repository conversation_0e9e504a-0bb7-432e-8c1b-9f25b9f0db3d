# OpenHandsを始める

[OpenHandsを実行](./installation)して、
[LLMをセットアップ](./installation#setup)しました。次は何をしますか？

OpenHandsはさまざまなエンジニアリングタスクを支援できます。しかし、この技術はまだ新しく、複雑なタスクを独立して処理できるエージェントの実現にはまだ遠い道のりがあります。エージェントが得意とすることと、サポートが必要な部分を理解することが重要です。

## Hello World

まずは簡単な「hello world」の例から始めましょう。思ったより難しいかもしれません！

エージェントに次のようにプロンプトしてみましょう：
> 「hello world!」と表示するbashスクリプトhello.shを書いてください

エージェントはスクリプトを作成し、適切な権限を設定し、実行して出力を確認します。

コードを改良するためにエージェントにさらにプロンプトを続けることができます。これはエージェントと作業する素晴らしい方法です。シンプルに始めて、反復していきましょう。

> hello.shを修正して、最初の引数として名前を受け取るようにしてください。ただし、デフォルトは「world」にしてください

また、必要な言語を使用することもできます。エージェントは環境のセットアップに時間がかかる場合があります。

> hello.shをRubyスクリプトに変換して、実行してください

## ゼロからの構築

エージェントは「グリーンフィールド」タスク、つまり既存のコードについてのコンテキストが不要で、
ゼロから始められるタスクに優れています。
シンプルなタスクから始めて、そこから反復していきましょう。欲しいものと技術スタックについて具体的に指示しましょう。

例えば、TODOアプリを構築するとします：

> フロントエンドのみのTODOアプリをReactで構築してください。すべての状態はlocalStorageに保存してください。

基本的な構造ができたら、さらに改良を続けます：

> 各タスクにオプションの期限を追加できるようにしてください。

通常の開発と同様に、コードを頻繁にコミットしてプッシュしましょう。
これにより、エージェントが道を外れた場合でも、常に古い状態に戻すことができます。
エージェントにコミットとプッシュを依頼することもできます：

> 変更をコミットして、「feature/due-dates」という新しいブランチにプッシュしてください

## 新しいコードの追加

OpenHandsは既存のコードベースに新しいコードを追加するのに優れています。

例えば、コードをリントするGitHub actionを追加するようOpenHandsに依頼できます。言語を判断するためにコードベースをチェックし、`./github/workflows/lint.yml`に新しいファイルを作成するかもしれません。

> このリポジトリのコードをリントするGitHub actionを追加してください。

一部のタスクにはより多くのコンテキストが必要です。OpenHandsはlsやgrepなどのコマンドを使用して検索できますが、前もってコンテキストを提供することで作業が速くなり、トークンの使用量が減ります。

> ./backend/api/routes.jsを修正して、すべてのタスクのリストを返す新しいルートを追加してください。

> ./frontend/componentsディレクトリに、Widgetのリストを表示する新しいReactコンポーネントを追加してください。
> 既存のWidgetコンポーネントを使用する必要があります。

## リファクタリング

OpenHandsは小さな単位でのコードリファクタリングに優れています。コードベース全体を再設計するよりも、
長いファイルや関数を分割したり、変数名を変更したりする方が効果的です。

> ./app.goの一文字変数をすべて改名してください。

> widget.phpの`build_and_deploy_widgets`関数を`build_widgets`と`deploy_widgets`の2つの関数に分割してください。

> ./api/routes.jsを各ルートごとに別々のファイルに分割してください。

## バグ修正

OpenHandsはバグの追跡と修正を支援できますが、バグ修正は難しく、多くの場合より多くのコンテキストが必要です。
すでに問題を診断していて、OpenHandsにロジックを処理してもらうだけの場合は役立ちます。

> `/subscribe`エンドポイントのメールフィールドが.ioドメインを拒否しています。これを修正してください。

> ./app.pyの`search_widgets`関数が大文字と小文字を区別する検索を行っています。大文字と小文字を区別しないようにしてください。

バグ修正には、テスト駆動開発が非常に役立ちます。エージェントに新しいテストを書いてもらい、バグが修正されるまで反復することができます：

> `hello`関数が空の文字列でクラッシュします。このバグを再現するテストを書いて、コードを修正してテストに合格するようにしてください。

## その他

OpenHandsはほぼすべてのコーディングタスクを支援できますが、最良の結果を得るには練習が必要です。
以下のヒントを心に留めておきましょう：
* タスクを小さく保つ。
* 具体的に指示する。
* 十分なコンテキストを提供する。
* 頻繁にコミットしてプッシュする。

OpenHandsを最大限に活用する方法についての詳細は、[プロンプトのベストプラクティス](./prompting/prompting-best-practices)をご覧ください。
