# OpenHandsの実行

## システム要件

- [Docker Desktopをサポート](https://docs.docker.com/desktop/setup/install/mac-install/#system-requirements)するMacOS
- Linux
- [WSL](https://learn.microsoft.com/en-us/windows/wsl/install)と[Docker Desktopをサポート](https://docs.docker.com/desktop/setup/install/windows-install/#system-requirements)するWindows

OpenHandsを実行するには、最新のプロセッサと最低**4GB RAM**を搭載したシステムが推奨されます。

## 前提条件

<details>
  <summary>MacOS</summary>

  **Docker Desktop**

  1. [MacにDocker Desktopをインストール](https://docs.docker.com/desktop/setup/install/mac-install)します。
  2. Docker Desktopを開き、`設定 > 詳細設定`に移動して、`デフォルトのDockerソケットの使用を許可する`が有効になっていることを確認します。
</details>

<details>
  <summary>Linux</summary>

  :::note
  Ubuntu 22.04でテスト済み。
  :::

  **Docker Desktop**

  1. [LinuxにDocker Desktopをインストール](https://docs.docker.com/desktop/setup/install/linux/)します。

</details>

<details>
  <summary>Windows</summary>

  **WSL**

  1. [WSLをインストール](https://learn.microsoft.com/en-us/windows/wsl/install)します。
  2. PowerShellで`wsl --version`を実行し、`Default Version: 2`であることを確認します。

  **Docker Desktop**

  1. [WindowsにDocker Desktopをインストール](https://docs.docker.com/desktop/setup/install/windows-install)します。
  2. Docker Desktopを開き、`設定`で以下を確認します：
  - 一般：`WSL 2ベースのエンジンを使用する`が有効になっている。
  - リソース > WSL統合：`デフォルトのWSLディストリビューションとの統合を有効にする`が有効になっている。

  :::note
  アプリを起動するための以下のdockerコマンドは、WSLターミナル内で実行する必要があります。
  :::

</details>

## アプリの起動

OpenHandsを実行する最も簡単な方法はDockerを使用することです。

```bash
docker pull docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik

docker run -it --rm --pull=always \
    -e SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik \
    -e LOG_ALL_EVENTS=true \
    -v /var/run/docker.sock:/var/run/docker.sock \
    -v ~/.openhands-state:/.openhands-state \
    -p 3000:3000 \
    --add-host host.docker.internal:host-gateway \
    --name openhands-app \
    docker.all-hands.dev/all-hands-ai/openhands:0.39
```

OpenHandsは http://localhost:3000 で実行されています！

また、[OpenHandsをローカルファイルシステムに接続](https://docs.all-hands.dev/modules/usage/runtimes/docker#connecting-to-your-filesystem)したり、
OpenHandsを[ヘッドレスモード](https://docs.all-hands.dev/modules/usage/how-to/headless-mode)でスクリプト実行したり、
[使いやすいCLI](https://docs.all-hands.dev/modules/usage/how-to/cli-mode)を介して操作したり、
[GitHubアクション](https://docs.all-hands.dev/modules/usage/how-to/github-action)でタグ付けされた課題に対して実行したりすることもできます。

## セットアップ

OpenHandsを起動した後、`LLMプロバイダー`と`LLMモデル`を選択し、対応する`APIキー`を入力する**必要があります**。
これは、初期設定ポップアップ時に行うか、UIの`設定`ボタン（歯車アイコン）を選択して行うことができます。

必要なモデルがリストに存在しない場合は、`詳細設定`をトグルして、正しいプレフィックスを付けて
`カスタムモデル`テキストボックスに手動で入力することができます。
`詳細設定`では、必要に応じて`ベースURL`を指定することもできます。

### APIキーの取得

OpenHandsはほとんどの言語モデルにアクセスするためにAPIキーが必要です。推奨プロバイダーからAPIキーを取得する方法は次のとおりです：

#### Anthropic (Claude)

1. [Anthropicアカウントを作成](https://console.anthropic.com/)します。
2. [APIキーを生成](https://console.anthropic.com/settings/keys)します。
3. [課金を設定](https://console.anthropic.com/settings/billing)します。

コストを管理するために使用制限を設定することを検討してください。

#### OpenAI

1. [OpenAIアカウントを作成](https://platform.openai.com/)します。
2. [APIキーを生成](https://platform.openai.com/api-keys)します。
3. [課金を設定](https://platform.openai.com/account/billing/overview)します。

これで[OpenHandsを使い始める](./getting-started)準備ができました。

## バージョン

[上記のdockerコマンド](./installation#start-the-app)は、OpenHandsの最新の安定版リリースを取得します。他のオプションもあります：
- 特定のリリースの場合、`openhands:$VERSION`と`runtime:$VERSION`の$VERSIONをバージョン番号に置き換えます。
SemVerを使用しているため、`0.9`は自動的に最新の`0.9.x`リリースを指し、`0`は最新の`0.x.x`リリースを指します。
- 最新の開発バージョンの場合、`openhands:$VERSION`と`runtime:$VERSION`の$VERSIONを`main`に置き換えます。
このバージョンは不安定であり、テストまたは開発目的でのみ推奨されます。

開発ワークフローについては、[Development.md](https://github.com/All-Hands-AI/OpenHands/blob/main/Development.md)を参照してください。

問題がありますか？[トラブルシューティングガイド](https://docs.all-hands.dev/modules/usage/troubleshooting)をご確認ください。
