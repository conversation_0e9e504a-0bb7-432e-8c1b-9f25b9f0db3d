# 🏛️ システムアーキテクチャ

<div style={{ textAlign: 'center' }}>
  <img src="https://github.com/All-Hands-AI/OpenHands/assets/16201837/97d747e3-29d8-4ccb-8d34-6ad1adb17f38" alt="OpenHands System Architecture Diagram Jul 4 2024" />
  <p><em>OpenHandsシステムアーキテクチャ図（2024年7月4日）</em></p>
</div>

これはシステムアーキテクチャの高レベル概要です。システムはフロントエンドとバックエンドの2つの主要コンポーネントに分かれています。フロントエンドはユーザーインタラクションの処理と結果の表示を担当します。バックエンドはビジネスロジックの処理とエージェントの実行を担当します。

# フロントエンドアーキテクチャ {#frontend-architecture-en}

![system_architecture.svg](/img/system_architecture.svg)

この概要は主要コンポーネントとその相互作用を示すために簡略化されています。バックエンドアーキテクチャのより詳細な表示については、以下のバックエンドアーキテクチャセクションを参照してください。

# バックエンドアーキテクチャ {#backend-architecture-en}

_**免責事項**: バックエンドアーキテクチャは進行中の作業であり、変更される可能性があります。以下の図は、図のフッターに表示されているコミットに基づく現在のバックエンドアーキテクチャを示しています。_

![backend_architecture.svg](/img/backend_architecture.svg)

<details>
  <summary>この図の更新について</summary>
  <div>
    バックエンドアーキテクチャ図の生成は部分的に自動化されています。
    この図はpy2pumlツールを使用してコード内の型ヒントから生成されます。その後、図は手動でレビュー、調整され、PNGおよびSVGにエクスポートされます。

    ## 前提条件

    - openhandsが実行可能なPython環境が稼働していること
    （リポジトリのルートにあるREADME.mdファイルの指示に従って）
    - [py2puml](https://github.com/lucsorel/py2puml)がインストールされていること

## 手順

1.  リポジトリのルートから次のコマンドを実行して図を自動生成します：
    `py2puml openhands openhands > docs/architecture/backend_architecture.puml`

2.  生成されたファイルをPlantUMLエディタで開きます。例えば、PlantUML拡張機能を持つVisual Studio Codeや[PlantText](https://www.planttext.com/)などです。

3.  生成されたPUMLをレビューし、図に必要な調整をすべて行います（不足している部分の追加、ミスの修正、配置の改善）。
    _py2pumlはコード内の型ヒントに基づいて図を作成するため、型ヒントが不足または不正確な場合、不完全または不正確な図が生成される可能性があります。_

4.  新しい図と以前の図の差分をレビューし、変更が正しいかどうかを手動で確認します。
    _過去に図に手動で追加され、まだ関連性のある部分を削除しないように注意してください。_

5.  図を生成するために使用されたコミットのコミットハッシュを図のフッターに追加します。

6.  図をPNGおよびSVGファイルとしてエクスポートし、`docs/architecture`ディレクトリ内の既存の図を置き換えます。これは（例えば[PlantText](https://www.planttext.com/)で）行うことができます。

  </div>
</details>
