# GitLab インストール

このガイドでは、GitLabリポジトリ用にOpenHands Cloudをインストールおよび設定するプロセスについて説明します。

## 前提条件

- GitLabアカウント
- OpenHands Cloudへのアクセス

## インストール手順

1. [OpenHands Cloud](https://app.all-hands.dev)にログインします
2. まだGitLabアカウントを接続していない場合：
   - `GitLabに接続する`をクリックします
   - 利用規約を確認して同意します
   - OpenHands AIアプリケーションを承認します

## リポジトリアクセスの追加

特定のリポジトリへのアクセスをOpenHandsに許可できます：

1. `GitLabプロジェクトを選択`ドロップダウンをクリックし、`リポジトリを追加...`を選択します
2. 組織を選択し、OpenHandsにアクセスを許可する特定のリポジトリを選択します。
   - OpenHandsは以下のスコープで権限をリクエストします：
     - api：完全なAPIアクセス
     - read_user：ユーザー情報の読み取り
     - read_repository：リポジトリ情報の読み取り
     - write_repository：リポジトリへの書き込み
   - ユーザーのリポジトリアクセスは以下に基づいて付与されます：
     - リポジトリに付与された権限
     - ユーザーのGitLab権限（所有者/メンテナー/開発者）
3. `インストール＆承認`をクリックします

## リポジトリアクセスの変更

リポジトリアクセスはいつでも変更できます：
* 同じ`GitLabプロジェクトを選択 > リポジトリを追加`ワークフローを使用する、または
* 設定ページにアクセスし、`GitLab設定`セクションで`GitLabリポジトリを設定する`を選択します。

## GitLabでのOpenHandsの使用

リポジトリアクセスを許可すると、GitLabリポジトリでOpenHandsを使用できます。

GitLab課題とマージリクエストでOpenHandsを使用する方法の詳細については、[クラウド課題リゾルバー](./cloud-issue-resolver.md)のドキュメントを参照してください。

## 次のステップ

- [クラウドUIにアクセスする](./cloud-ui.md)でウェブインターフェースと対話する
- [クラウド課題リゾルバーを使用する](./cloud-issue-resolver.md)でコード修正を自動化し、支援を受ける
- [クラウドAPIを使用する](./cloud-api.md)でプログラムによりOpenHandsと対話する
