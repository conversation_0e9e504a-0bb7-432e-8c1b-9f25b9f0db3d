# OpenHandsについて

## 研究戦略

LLMによる本番グレードのアプリケーションの完全な複製を達成することは複雑な取り組みです。私たちの戦略は以下を含みます：

- **コア技術研究：** コード生成と処理の技術的側面を理解し改善するための基礎研究に焦点を当てる。
- **タスク計画：** バグ検出、コードベース管理、最適化のための能力開発。
- **評価：** エージェントをより良く理解し改善するための包括的な評価指標の確立。

## デフォルトエージェント

現在のデフォルトエージェントは[CodeActAgent](agents)で、コードの生成とファイル処理が可能です。

## 使用技術

OpenHandsは強力なフレームワークとライブラリの組み合わせを使用して構築されており、その開発のための堅牢な基盤を提供しています。以下はプロジェクトで使用されている主要な技術です：

![FastAPI](https://img.shields.io/badge/FastAPI-black?style=for-the-badge) ![uvicorn](https://img.shields.io/badge/uvicorn-black?style=for-the-badge) ![LiteLLM](https://img.shields.io/badge/LiteLLM-black?style=for-the-badge) ![Docker](https://img.shields.io/badge/Docker-black?style=for-the-badge) ![Ruff](https://img.shields.io/badge/Ruff-black?style=for-the-badge) ![MyPy](https://img.shields.io/badge/MyPy-black?style=for-the-badge) ![LlamaIndex](https://img.shields.io/badge/LlamaIndex-black?style=for-the-badge) ![React](https://img.shields.io/badge/React-black?style=for-the-badge)

これらの技術の選択は進行中であり、プロジェクトの進化に伴い、追加の技術が加えられたり、既存のものが削除されたりする可能性があることにご注意ください。私たちはOpenHandsの能力を高めるために、最も適切で効率的なツールを採用するよう努めています。

## ライセンス

MIT [ライセンス](https://github.com/All-Hands-AI/OpenHands/blob/main/LICENSE)の下で配布されています。
