@startuml "System Architecture"


node frontend as frontend{

    component App

    package components{

        component Terminal

        component ChatInterface

        component BannerSettings

    }

    package services{
        component chatService

        component settingsService

        chatService -[hidden]u-> settingsService
    }

    package socket

    App -> Terminal
    App -> ChatInterface
    App -> BannerSettings
    ChatInterface -> chatService
    BannerSettings -> settingsService
    Terminal -> socket
    chatService -d-> socket
    settingsService -d-> socket
    services -[hidden]d-> socket

    Terminal -[hidden]u-> ChatInterface
    ChatInterface -[hidden]u-> BannerSettings



    interface "HTTP (:3001)" as HTTP
    HTTP - App

}

node backend{
    package server as serverpackage{
        component Server

        'defined in server/server.py, port is defined at startup with uvicorn
        interface "Client WS\n(:3000/ws)" as client_socket
        client_socket - Server


    }
    node AgentController{

    }
    Server -d-> AgentController
}


socket -d-> client_socket: connects to \n VITE_TERMINAL_WS_URL

@enduml
