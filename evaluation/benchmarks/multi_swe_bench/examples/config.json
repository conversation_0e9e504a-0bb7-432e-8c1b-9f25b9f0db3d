{"mode": "evaluation", "workdir": "./data/workdir", "patch_files": ["./data/patches/<your_patch_file>.jsonl"], "dataset_files": ["./data/patches/<to_evaluate_dataset_file>.jsonl"], "force_build": false, "output_dir": "./data/dataset", "specifics": [], "skips": [], "repo_dir": "./data/repos", "need_clone": false, "global_env": [], "clear_env": true, "stop_on_error": true, "max_workers": 8, "max_workers_build_image": 8, "max_workers_run_instance": 8, "log_dir": "./data/logs", "log_level": "DEBUG"}