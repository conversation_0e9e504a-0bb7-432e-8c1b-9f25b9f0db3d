{"org": "ponylang", "repo": "ponyc", "number": "4595", "fix_patch": "diff --git a/src/libponyc/ast/parser.c b/src/libponyc/ast/parser.c\nindex 9852922f..2c37d6b8 100644\n--- a/src/libponyc/ast/parser.c\n+++ b/src/libponyc/ast/parser.c\n@@ -693,6 +693,7 @@ DEF(idseqsingle);\n   AST_NODE(TK_LET);\n   TOKEN(\"variable name\", TK_ID);\n   AST_NODE(TK_NONE);  // Type\n+  SET_FLAG(AST_FLAG_IN_PARENS);\n   DONE();\n \n // idseq"}
{"org": "ponylang", "repo": "ponyc", "number": "4593", "fix_patch": "diff --git a/packages/cli/command_parser.pony b/packages/cli/command_parser.pony\nindex a5acce8e..fa97808b 100644\n--- a/packages/cli/command_parser.pony\n+++ b/packages/cli/command_parser.pony\n@@ -100,6 +100,7 @@ class CommandParser\n             | let cs: CommandSpec box =>\n               return CommandParser._sub(cs, this).\n                 _parse_command(tokens, options, args, envsmap, opt_stop)\n+// Correctly handle parent default options\n             end\n           else\n             return SyntaxError(token, \"unknown command\")"}
{"org": "ponylang", "repo": "ponyc", "number": "4588", "fix_patch": "diff --git a/src/libponyc/expr/match.c b/src/libponyc/expr/match.c\nindex 7d16066f..c2ec7056 100644\n--- a/src/libponyc/expr/match.c\n+++ b/src/libponyc/expr/match.c\n@@ -314,8 +314,10 @@ static ast_t* make_pattern_type(pass_opt_t* opt, ast_t* pattern)\n     case TK_DONTCAREREF:\n     case TK_MATCH_CAPTURE:\n     case TK_MATCH_DONTCARE:\n+      if (ast_id(pattern_type) == TK_ISO) pattern_type = set_cap_and_ephemeral(pattern_type, TK_TRN, TK_EPHEMERAL);\n       return pattern_type;\n \n+\n     case TK_TUPLE:\n     {\n       ast_t* pattern_child = ast_child(pattern);"}
