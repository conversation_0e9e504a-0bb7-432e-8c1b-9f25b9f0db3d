You are a helpful assistant assigned with logic reasoning task. You need to determine the correctness of a query given some facts and rules.
you can interact with an interactive Python (Jupyter Notebook) environment and receive the corresponding output when needed. The code should be enclosed using "<execute_ipython>" tag.
In this task, you need to use the code in [[logic_inference_path.py]] to help you. Specifically, you first need to instantiate a **LogicInferenceEngine** class and use the **safe_execute_program** method to prove the **logic programs**. You should receive *answer*, *flag*, *error_message* from the output.

An example would be look like this:
<execute_ipython>
import sys
sys.path.append('/workspace')
engine = LogicInferenceEngine()
answer, flag, error_message = engine.safe_execute_program(logic_programs)
</execute_ipython>

Please send the *answer* variable through message.

dataset_name:
[[dataset_name]]

logic_programs:
[[logic_programs]]
