# Workflow that builds and deploys the documentation website
name: Deploy Docs to GitHub Pages

# * Always run on "main"
# * Run on PRs that target the "main" branch and have changes in the "docs" folder or this workflow
on:
  push:
    branches:
      - main
  pull_request:
    paths:
      - 'docs/**'
      - '.github/workflows/deploy-docs.yml'
    branches:
      - main

# If triggered by a PR, it will be in the same group. However, each commit on main will be in its own unique group
concurrency:
  group: ${{ github.workflow }}-${{ (github.head_ref && github.ref) || github.run_id }}
  cancel-in-progress: true

jobs:
  # Build the documentation website
  build:
    if: github.repository == 'All-Hands-AI/OpenHands'
    name: Build Docusaurus
    runs-on: blacksmith-4vcpu-ubuntu-2204
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: useblacksmith/setup-node@v5
        with:
          node-version: 18
          cache: npm
          cache-dependency-path: docs/package-lock.json
      - name: Set up Python
        uses: useblacksmith/setup-python@v6
        with:
          python-version: '3.12'
      - name: Install dependencies
        run: cd docs && npm ci
      - name: Build website
        run: cd docs && npm run build
      - name: Upload Build Artifact
        if: github.ref == 'refs/heads/main'
        uses: actions/upload-pages-artifact@v3
        with:
          path: docs/build

  # Deploy the documentation website
  deploy:
    if: github.ref == 'refs/heads/main' && github.repository == 'All-Hands-AI/OpenHands'
    name: Deploy to GitHub Pages
    runs-on: blacksmith-4vcpu-ubuntu-2204
    # This job only runs on "main" so only run one of these jobs at a time
    # otherwise it will fail if one is already running
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
    needs: build
    # Grant GITHUB_TOKEN the permissions required to make a Pages deployment
    permissions:
      pages: write # to deploy to Pages
      id-token: write # to verify the deployment originates from an appropriate source
    # Deploy to the github-pages environment
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
