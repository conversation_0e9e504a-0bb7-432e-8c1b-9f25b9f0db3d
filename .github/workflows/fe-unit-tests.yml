# Workflow that runs frontend unit tests
name: Run Frontend Unit Tests

# * Always run on "main"
# * Run on PRs that have changes in the "frontend" folder or this workflow
on:
  push:
    branches:
      - main
  pull_request:
    paths:
      - 'frontend/**'
      -  '.github/workflows/fe-unit-tests.yml'

# If triggered by a PR, it will be in the same group. However, each commit on main will be in its own unique group
concurrency:
  group: ${{ github.workflow }}-${{ (github.head_ref && github.ref) || github.run_id }}
  cancel-in-progress: true

jobs:
  # Run frontend unit tests
  fe-test:
    name: FE Unit Tests
    runs-on: blacksmith-4vcpu-ubuntu-2204
    strategy:
      matrix:
        node-version: [20, 22]
      fail-fast: true
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: useblacksmith/setup-node@v5
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci
      - name: Run TypeScript compilation
        working-directory: ./frontend
        run: npm run make-i18n && tsc
      - name: Run tests and collect coverage
        working-directory: ./frontend
        run: npm run test:coverage
