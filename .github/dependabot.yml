version: 2
updates:
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
    groups:
      # put packages in their own group if they have a history of breaking the build or needing to be reverted
      pre-commit:
        patterns:
          - "pre-commit"
      browsergym:
        patterns:
          - "browsergym*"
      mcp-packages:
        patterns:
          - "mcp"
          - "mcpm"
      security-all:
        applies-to: "security-updates"
        patterns:
          - "*"
      version-all:
        applies-to: "version-updates"
        patterns:
          - "*"

  - package-ecosystem: "npm"
    directory: "/frontend"
    schedule:
      interval: "daily"
    open-pull-requests-limit: 1
    groups:
      docusaurus:
        patterns:
          - "*docusaurus*"
      eslint:
        patterns:
          - "*eslint*"
      security-all:
        applies-to: "security-updates"
        patterns:
          - "*"
      version-all:
        applies-to: "version-updates"
        patterns:
          - "*"

  - package-ecosystem: "npm"
    directory: "/docs"
    schedule:
      interval: "weekly"
      day: "wednesday"
    open-pull-requests-limit: 1
    groups:
      docusaurus:
        patterns:
          - "*docusaurus*"
      eslint:
        patterns:
          - "*eslint*"
      security-all:
        applies-to: "security-updates"
        patterns:
          - "*"
      version-all:
        applies-to: "version-updates"
        patterns:
          - "*"

  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
